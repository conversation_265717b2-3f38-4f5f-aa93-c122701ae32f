"""
Agent的基类
"""
from typing import Optional

from chatbi.agent.config import AgentConfigs
from chatbi.data_source.base import DataSource
from chatbi.memory.base import BaseMemory
from chatbi.private.framework import FoundationLayer


class BaseAgent(FoundationLayer):
    """
    基础智能体类，封装核心组件和功能
    """

    def __init__(
            self,
            agent_configs: AgentConfigs,
            verbose: bool = False,
            user: Optional[str] = None,
            session_id: Optional[str] = None,
    ) -> None:

        if not isinstance(agent_configs, AgentConfigs):
            raise ValueError("agent_configs must be an instance of AgentConfigs")

        super().__init__(
            callback=agent_configs.callback,
            model=agent_configs.llm,
            verbose=verbose,
            logger=agent_configs.logger,
        )

        self.agent_configs: AgentConfigs = agent_configs
        self.memory: BaseMemory = agent_configs.memory
        self.datasource: Optional[DataSource] = agent_configs.datasource
        self.user: Optional[str] = user
        self.session_id: Optional[str] = session_id

    @property
    def name(self) -> str:
        return "基础智能体"

