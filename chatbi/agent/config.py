from typing import Optional
from dataclasses import dataclass

from chatbi.memory.base import BaseMemory
from chatbi.stream_responser import DataStreamer
from chatbi.llms.base import BaseLLM
from chatbi.data_source.base import DataSource
from chatbi.utils.logger import Logger


@dataclass(frozen=True)
class AgentConfigs:
    """
    配置和管理Agent的核心参数。
    存储和验证Agent运行所需的各种组件配置
    """
    llm: BaseLLM
    datasource: Optional[DataSource] = None
    callback: Optional[DataStreamer] = None
    memory: Optional[BaseMemory] = None
    logger: Optional[Logger] = None
    vision_llm: Optional[BaseLLM] = None

    def __post_init__(self) -> None:
        """
        初始化后的验证方法，确保核心组件的有效性

        Raises:
            ValueError: 如果llm参数未提供有效实例
        """
        if not isinstance(self.llm, BaseLLM):
            raise ValueError("llm must be an instance of BaseLLM")

    def __str__(self) -> str:

        components = [
            f"llm={self.llm.__class__.__name__}",
            f"datasource={'Set' if self.datasource else 'Not set'}",
            f"callback={'Set' if self.callback else 'Not set'}",
            f"memory={'Set' if self.memory else 'Not set'}",
            f"logger={'Set' if self.logger else 'Not set'}",
            f"vision_llm={'Set' if self.vision_llm else 'Not set'}"
        ]
        return f"AgentConfigs({', '.join(components)})"

    @property
    def is_vision_enabled(self) -> bool:
        return self.vision_llm is not None
