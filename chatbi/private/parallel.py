from chatbi.private.framework import FoundationLayer
from typing import Union, List
import threading


class ParallelFoundationLayer:
    def __init__(self, agent):
        self.agents: List = agent     # 存放FoundationLayer实例List

    def __or__(self, other):
        if isinstance(other, FoundationLayer):
            self.agents.append(other)
            return self
        elif isinstance(other, ParallelFoundationLayer):
            self.agents.extend(other.agents)
            return self
        else:
            raise TypeError("The right-hand side of the 'or' must be an instance of FDLayer or Parallel FDlayer.")

    def run(self, params: Union[dict, List[dict]]):
        """
        并行运行所有 FoundationLayer 实例。
        例如:
            agents = SQLAgent1 | SQLAgent2
            params = [{'query': 'xxx'}, {'query': 'yyy'}]  # 每个 agent 用不同的 params
            # 或
            params = {'query': 'xxx'}  # 所有 agent 用相同的 params
            results = agents.run(params)

        Args:
            params (Union[dict, List[dict]]): 如果是 dict，所有 agent 使用相同参数；
                                             如果是 List[dict]，每个 agent 使用对应索引的参数。

        Returns:
            tuple: 按 agent 顺序返回结果的元组。

        Raises:
            ValueError: 如果 params 是列表但长度与 agent 数量不匹配。
        """

        if isinstance(params, list) and len(params) != len(self.agents):
            raise ValueError(f"Length of params ({len(params)}) must match number of agents ({len(self.agents)})")

        results = [None] * len(self.agents)     # 初始化结果列表
        threads = []
        output_lock = threading.Lock()

        def thread_run(agent, index, params_inner):
            result = agent.run(params_inner)
            with output_lock:
                results[index] = result    # 使用索引来存储结果

        # 为每一个FoundationLayer创建并运行线程，并传递索引
        for index, agent in enumerate(self.agents):
            if isinstance(params, dict):
                thread = threading.Thread(target=thread_run, args=(agent, index, params))
            else:  # 并行 agent 可使用不同的 query
                thread = threading.Thread(target=thread_run, args=(agent, index, params[index]))

            threads.append(thread)
            thread.start()

        # 等待所有线程完成
        for thread in threads:
            thread.join()

        results = tuple(results)  # 确保结果是按照顺序的
        return results
