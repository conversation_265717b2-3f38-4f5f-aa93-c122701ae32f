from typing import Dict, Any, List, Optional, Set
from abc import ABC, abstractmethod
import json
from chatbi.private.config.validators import ConfigValidator, ValidationError


class BaseConfig(ABC):
    """配置基类"""
    def __init__(self):
        self._validators: Dict[str, List[callable]] = {}
        self._observers: Set[callable] = set()
        self._frozen = False

    def add_validator(self, field: str, validator: callable):
        if field not in self._validators:
            self._validators[field] = []
        self._validators[field].append(validator)

    def add_observer(self, observer: callable):
        self._observers.add(observer)

    def remove_observer(self, observer: callable):
        self._observers.discard(observer)

    def _notify_observers(self, field: str, value: Any):
        for observer in self._observers:
            observer(field, value)

    def freeze(self):
        self._frozen = True

    def unfreeze(self):
        self._frozen = False

    @abstractmethod
    def validate(self) -> List[ValidationError]:
        pass

    @abstractmethod
    def to_dict(self) -> Dict[str, Any]:
        pass

    @abstractmethod
    def from_dict(self, data: Dict[str, Any]):
        pass

