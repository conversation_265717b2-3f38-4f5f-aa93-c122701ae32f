from typing import Any, Optional, Type, get_type_hints
from dataclasses import dataclass
import inspect


@dataclass
class ValidationError:
    field: str
    message: str
    value: Any


class ConfigValidator:
    """配置验证器"""

    @staticmethod
    def validate_type(value: Any, expected_type: Type) -> Optional[str]:
        """验证值类型"""
        if not isinstance(value, expected_type):
            return f"Expected type {expected_type.__name__}, got {type(value).__name__}"
        return None

    @staticmethod
    def validate_instance(value: Any, base_class: Type) -> Optional[str]:
        """验证实例类型"""
        if not isinstance(value, base_class):
            return f"Value must be an instance of {base_class.__name__}"
        return None

    @classmethod
    def validate_callable(cls, value: Any) -> Optional[str]:
        """验证可调用对象"""
        if not callable(value):
            return "Value must be callable"
        return None
