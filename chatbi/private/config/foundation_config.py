from typing import Optional, Dict, Any, List
import dataclasses
import json
from chatbi.private.config.base import BaseConfig
from chatbi.private.config.validators import ConfigValidator, ValidationError
from chatbi.llms.base import BaseLLM
from chatbi.rag.embedding.embedding import EmbeddingModel
from chatbi.stream_responser import DataStreamer
from chatbi.es.es_client import ElasticsearchClient
# from authorization.model_user_base_info import UserInfo


@dataclasses.dataclass
class ConfigData:
    """配置数据类"""
    llm: Optional[BaseLLM] = None
    embedding_model: Optional[EmbeddingModel] = None
    callback: Optional[DataStreamer] = None
    es_client: Optional[ElasticsearchClient] = None
    user: Optional[dict] = None


class FoundationConfig(BaseConfig):
    """Foundation层配置类"""

    def __init__(self):
        super().__init__()
        self._data = ConfigData()
        self._setup_validators()

        # 显式定义属性
        self.llm: Optional[BaseLLM] = self._data.llm
        self.embedding_model: Optional[EmbeddingModel] = self._data.embedding_model
        self.callback: Optional[DataStreamer] = self._data.callback
        self.es_client: Optional[ElasticsearchClient] = self._data.es_client
        self.user: Optional[dict] = self._data.user

    def _setup_validators(self):
        """设置验证器"""
        self.add_validator('llm',
                           lambda x: ConfigValidator.validate_instance(x, BaseLLM) if x else None)
        self.add_validator('embedding_model',
                           lambda x: ConfigValidator.validate_instance(x, EmbeddingModel) if x else None)
        self.add_validator('callback',
                           lambda x: ConfigValidator.validate_instance(x, DataStreamer) if x else None)
        self.add_validator('es_client',
                           lambda x: ConfigValidator.validate_instance(x, ElasticsearchClient) if x else None)
        self.add_validator('user',
                           lambda x: ConfigValidator.validate_instance(x, dict) if x else None)

    def __getattr__(self, name: str) -> Any:
        """获取属性值"""
        if hasattr(self._data, name):
            return getattr(self._data, name)
        raise AttributeError(f"'{self.__class__.__name__}' object has no attribute '{name}'")

    def __setattr__(self, name: str, value: Any):
        """设置属性值"""
        if name.startswith('_'):
            super().__setattr__(name, value)
            return

        if hasattr(self._data, name):
            if self._frozen:
                raise ValueError("Configuration is frozen and cannot be modified")

            # 验证新值
            validators = self._validators.get(name, [])
            for validator in validators:
                error_msg = validator(value)
                if error_msg:
                    raise ValueError(f"Invalid value for {name}: {error_msg}")

            setattr(self._data, name, value)
            self._notify_observers(name, value)
        else:
            super().__setattr__(name, value)

    def validate(self) -> List[ValidationError]:
        """验证所有配置项"""
        errors = []
        for field in dataclasses.fields(self._data):
            value = getattr(self._data, field.name)
            validators = self._validators.get(field.name, [])
            for validator in validators:
                error_msg = validator(value)
                if error_msg:
                    errors.append(ValidationError(field.name, error_msg, value))
        return errors

    def update(self, **kwargs):
        """更新配置"""
        if self._frozen:
            raise ValueError("Configuration is frozen and cannot be modified")

        for key, value in kwargs.items():
            if hasattr(self._data, key):
                setattr(self, key, value)
            else:
                raise AttributeError(f"{key} is not a valid configuration field")

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典形式"""
        return {
            field.name: str(getattr(self._data, field.name))
            for field in dataclasses.fields(self._data)
        }

    def from_dict(self, data: Dict[str, Any]):
        """从字典加载配置"""
        for key, value in data.items():
            if hasattr(self._data, key):
                setattr(self, key, value)

    def to_json(self) -> str:
        """转换为JSON字符串"""
        return json.dumps(self.to_dict(), indent=4, ensure_ascii=False)
