class BaseError(Exception):
    """基础异常类"""
    pass


class APIError(BaseError):
    """调用 API 时发生错误"""
    def __init__(self, status_code=-1, message="API 请求失败"):
        self.status_code = status_code
        self.message = f"{message}，状态码：{status_code}"
        super().__init__(self.message)


class APIInvalidResponse(APIError):
    """API 返回了无效的响应"""
    def __init__(self, message="API 返回无效响应"):
        super().__init__(status_code=400, message=message)


class APIAuthenticationError(APIError):
    """API 认证失败"""
    def __init__(self, message="API 认证失败，请检查 API 密钥"):
        super().__init__(status_code=401, message=message)


class APIParameterError(APIError):
    """参数错误"""
    def __init__(self, message="API输入参数错误"):
        super().__init__(status_code=422, message=message)
