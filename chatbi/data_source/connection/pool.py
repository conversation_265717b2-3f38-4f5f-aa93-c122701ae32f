"""
pool:
source - data_source (DataSource)
       - expiration_time (timestamp)
       - last_used (timestamp)
       - key (str)
"""

import threading
import time
from collections import OrderedDict
from typing import Optional, Any, Dict
from chatbi.data_source.base import DataSource
from chatbi.redis.redis_client import RedisClient

from chatbi.utils.printf import printf


class DataSourceConnectionPool:
    def __init__(self,
                 max_connections: int = 100,
                 default_expiration: int = 3600,
                 clean_interval: int = 600,
                 sync_interval: int = 600,):
        """
        初始化连接池
        :param max_connections: 最大连接数
        :param default_expiration: 默认的连接过期时间（秒）
        :param clean_interval: 清理过期连接的时间间隔（秒）
        :param sync_interval: 定期同步Redis数据的时间间隔（秒）
        """
        self.max_connections = max_connections
        self.default_expiration = default_expiration
        self.pool: OrderedDict[str, Dict[str, Any]] = OrderedDict()
        self.lock = threading.Lock()
        self.clean_interval = clean_interval
        self.sync_interval = sync_interval
        self._stop_cleaner = threading.Event()
        self._stop_syncer = threading.Event()
        self._start_cleaner_thread()
        # self._start_syncer_thread()

    def _start_cleaner_thread(self):
        def cleaner():
            while not self._stop_cleaner.is_set():
                time.sleep(self.clean_interval)
                self.clear_expired_connections()
        threading.Thread(target=cleaner, daemon=True).start()

    # def _start_syncer_thread(self):
    #     def syncer():
    #         while not self._stop_syncer.is_set():
    #             time.sleep(self.sync_interval)
    #             self.sync_all_data_sources()
    #     threading.Thread(target=syncer, daemon=True).start()

    def stop_cleaner_thread(self):
        """停止清理线程"""
        self._stop_cleaner.set()

    def stop_syncer_thread(self):
        """停止同步线程"""
        self._stop_syncer.set()

    def get_connection(self, source: str, key: str = None) -> Optional[Any]:
        """
        获取指定来源的 DataSource 实例
        :param source: 数据源的唯一标识符
        :param key: 数据源的密钥
        :return: DataSource 实例或 None
        """
        with self.lock:
            entry = self.pool.get(source)
            # print(self.pool.items())
            if entry:
                if entry['key']:
                    if key != entry['key']:
                        raise ValueError('密钥错误')
                data_source = entry['data_source']
                if not data_source:
                    print(f"连接 '{source}' 不可用，正在移除")
                    del self.pool[source]
                else:
                    entry['last_used'] = time.time()
                    self.pool.move_to_end(source)
                    return data_source
            return None

    def add_connection(self, source: str, data_source: DataSource,
                       expiration_time: Optional[int] = None,
                       key: Optional[str] = None):
        """
        添加新的 DataSource 实例到连接池
        :param source: 数据源的唯一标识符
        :param data_source: DataSource 实例
        :param expiration_time: 连接的过期时间（秒），-1表示永不过期，默认为 default_expiration
        :param key: Source对应的密钥
        """
        if source in self.pool:
            raise ValueError(f'{source} 已在数据源中存在，请换个名字试试吧。')
        if not isinstance(data_source, DataSource):
            raise ValueError('data_source must be DataSource')
        with self.lock:
            if len(self.pool) >= self.max_connections:
                removed_source, _ = self.pool.popitem(last=False)
                print(f"连接池已满，移除最久未使用的连接 '{removed_source}'")
            if expiration_time is None:
                expiration_time = self.default_expiration
            expiration_timestamp = -1 if expiration_time == -1 else time.time() + expiration_time
            self.pool[source] = {
                'data_source': data_source,
                'expiration_time': expiration_timestamp,
                'last_used': time.time(),
                'key': key
            }
            printf(title='连接池添加成功', color='green', message=f"添加数据源 '{source}' 到连接池")
            # print(f"添加连接 '{source}' 到连接池")

    def remove_connection(self, source: str) -> bool:
        """移除指定的 DataSource 实例

        :param source: 要移除的数据源的名称
        :return: 如果移除成功返回 True，如果数据源不存在返回 False
        """
        with self.lock:
            removed = self.pool.pop(source, None)
            if removed is not None:
                print(f"移除连接 '{source}'")
                return True
            else:
                print(f"数据源 '{source}' 不存在")
                return False

    def clear_expired_connections(self):
        """清除所有已过期的连接"""
        with self.lock:
            current_time = time.time()
            expired_sources = [src for src, entry in self.pool.items()
                               if entry['expiration_time'] != -1 and current_time >= entry['expiration_time']]
            for src in expired_sources:
                del self.pool[src]
                print(f"清除过期的连接 '{src}'")

    def clear_all_connections(self):
        """清除所有连接"""
        with self.lock:
            self.pool.clear()
            print("清除所有连接")

    def get_pool_status(self) -> Dict[str, Any]:
        """获取连接池的状态信息"""
        with self.lock:
            return {
                'total_connections': len(self.pool),
                'max_connections': self.max_connections,
                'connections': list(self.pool.keys()),
                'key': None
            }

    def get_all_datasource_info(self) -> Dict[str, Any]:
        """用于获取连接池中的所有datasource的名字和类型"""
        with self.lock:
            datasource_info = {}
            for source, entry in self.pool.items():
                data_source = entry['data_source']
                datasource_info[source] = {
                    'type': data_source.database
                }
            return datasource_info

    def __enter__(self):
        """上下文管理器入口"""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器退出，清理资源"""
        self.stop_cleaner_thread()
        self.stop_syncer_thread()
        self.clear_all_connections()





