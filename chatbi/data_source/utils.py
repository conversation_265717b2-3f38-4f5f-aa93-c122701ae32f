import inspect
import pandas as pd
from typing import List
from pandas.api.types import infer_dtype


def get_class_details(cls) -> str:
    """
    获取类的所有方法、属性及其他内容，并返回格式化的字符串
    Args:
        cls: 要分析的类

    Returns:
        str: 包含类的所有方法、属性及其他内容的格式化字符串
    """
    # 获取父类
    super_classes = cls.__bases__
    details = {
        'Parent Classes': [super_class.__name__ for super_class in super_classes],
        'Methods': [],
        'Attributes': [],
        'Class Variables': []
    }

    # 获取所有方法
    all_methods = inspect.getmembers(cls, predicate=inspect.isfunction)
    for name, func in all_methods:
        try:
            source_code = inspect.getsource(func)
            details['Methods'].append((name, source_code))
        except OSError as e:
            details['Methods'].append((name, f"Error retrieving source: {e}"))

    # 获取所有类属性和变量
    all_attributes = inspect.getmembers(cls, predicate=lambda a: not(inspect.isroutine(a)))
    for name, attr in all_attributes:
        if not name.startswith('__') and not inspect.isfunction(attr):
            details['Class Variables'].append((name, repr(attr)))

    # 获取实例属性
    instance_attributes = vars(cls)
    for name, value in instance_attributes.items():
        details['Attributes'].append((name, repr(value)))

    # 构建返回的字符串
    result = []
    result.append(f"父类: {', '.join(details['Parent Classes'])}\n")

    # 方法
    result.append("方法:\n")
    for method_name, code in details['Methods']:
        result.append(f"方法名: {method_name}\n代码:\n{code}\n{'-' * 40}\n")
    return ''.join(result)


def column_info_to_dataframe(columns: List) -> pd.DataFrame:
    # 找到最长的列长度，忽略空的 example_data
    max_length = max((len(col.example_data) for col in columns if col.example_data), default=0)

    if max_length == 0:
        # 如果所有的 example_data 都是空的，返回空 DataFrame
        return pd.DataFrame()

    # 使用列名和示例数据构造字典
    data_dict = {}
    for col in columns:
        # 检查 example_data 是否存在且不为空
        if col.example_data:
            length = len(col.example_data)
            if length == 0:
                padded_data = [None] * max_length  # 如果 example_data 为空，填充 None
            else:
                # 如果长度不足，重复数据来填充
                padded_data = (col.example_data * (max_length // length)) + col.example_data[:(max_length % length)]
        else:
            # 如果 example_data 不存在，填充 None
            padded_data = [None] * max_length

        data_dict[col.name_en] = padded_data

    # 创建 DataFrame
    df = pd.DataFrame(data_dict)
    return df


def infer_sqlite_dtype(col):
    """根据Pandas列的数据类型推断SQLite数据类型"""
    dtype = infer_dtype(col)
    if dtype in ['integer', 'mixed-integer']:
        return 'INTEGER'
    elif dtype in ['floating', 'mixed-integer-float']:
        return 'REAL'
    elif dtype in ['boolean']:
        return 'INTEGER'  # SQLite没有布尔类型，通常用0和1表示
    elif dtype in ['datetime', 'datetime64', 'timedelta', 'timedelta64']:
        return 'DATETIME'
    elif dtype in ['complex']:
        return 'TEXT'  # SQLite不支持复数类型，存储为文本
    else:
        return 'TEXT'


if __name__ == '__main__':
    from chatbi.data_source.dbms.sqlite_datasource import SqliteDataSource
    print(get_class_details(SqliteDataSource))
