"""
DataSource的基类，在此基础上继承各类对接数据源的子类。
20240905 Tian

20250112 加入鉴权机制，实现逐表、列、行权限控制。
"""
import os.path
import numpy as np
import pandas as pd
from pydantic import BaseModel, Field, ConfigDict, model_validator
from typing import Optional, Any, Tuple, Dict
from abc import ABC, abstractmethod
import copy
from chatbi.data_source.utils import column_info_to_dataframe

from chatbi.similarity import (JaccardSimilarity,
                               LevenshteinSimilarity,
                               SubStringSimilarity,
                               )

from chatbi.utils.printf import printf
from chatbi.data_source.knowledge.extra_knowledge import ExtraKnowledge

# 配置ES客户端，后面将枚举值和业务逻辑、预置问答对全部放进去
from chatbi.es.es_client import ElasticsearchClient

# 配置Embedding模型
from chatbi.rag.embedding.embedding import EmbeddingModel

# 配置枚举值管理器
from chatbi.data_source.enums.enumeration import Enumeration

from chatbi.data_source.date_detection.date_detection import DateDetectionEnhancer

from chatbi.data_source.adaptive_schema.ds_description import AdaptiveSchemaGenerator
from chatbi.utils.temporary_file_manager import TempFileManager

from chatbi.data_source.access_control.access_info import AccessInfo
from typing import List, Union


class ColumnInfo(BaseModel):
    """定义字段的数据结构"""
    name_en: str  # 字段英文名
    name_cn: Optional[str] = None  # 字段中文名
    alias: Optional[List[str]] = None  # 字段的别名，列表 （240912新增，后续用于用户自定义配置）
    col_type: str = 'VARCHAR(256)'  # 字段类型
    col_comment: Optional[str] = None  # 字段注释  (column_description)
    example_data: Optional[List[Any]] = None  # 字段示例数据，非常重要
    enums: Optional[List[Any]] = None  # 字段的枚举值，非常重要
    is_required: bool = False  # 是否必选，默认为False
    value_description: Optional[str] = None  # 字段内值的描述
    is_foreign: Optional[bool] = False  # 是否为外键
    ref_table: Optional[str] = None  # 引用的表名
    ref_column: Optional[str] = None  # 引用的列名
    is_datetime: Optional[bool] = False  # 是否为时间相关的列
    datetime_pattern: Optional[str] = None  # 时间格式 如”YYYY-MM-DD“

    access_info: Optional[AccessInfo] = None  # 权限控制信息，用于后续鉴权

    model_config = ConfigDict(arbitrary_types_allowed=True)

    def __eq__(self, other):
        if isinstance(other, ColumnInfo):
            return self.name_en == other.name_en
        return False

    def __hash__(self):
        # 定义哈希值：根据 name_en 生成哈希
        return hash(self.name_en)


class ColumnManager(list):
    def __init__(self, columns: List[ColumnInfo]):
        super().__init__(columns)

    def __getitem__(self, key: Union[int, str]) -> ColumnInfo:
        """支持通过索引或字段英文名访问列"""
        if isinstance(key, int):
            return super().__getitem__(key)
        elif isinstance(key, str):
            for column in self:
                if column.name_en == key:
                    return column
            raise KeyError(f"Column with name_en '{key}' not found.")
        else:
            raise TypeError("Key must be an integer (index) or a string (column name).")

    def __setitem__(self, key: Union[int, str], value: ColumnInfo):
        """
        支持通过索引或字段英文名更新列。
        """
        if isinstance(key, int):
            super().__setitem__(key, value)
        elif isinstance(key, str):
            for idx, column in enumerate(self):
                if column.name_en == key:
                    super().__setitem__(idx, value)
                    return
            raise KeyError(f"Column with name_en '{key}' not found.")
        else:
            raise TypeError("Key must be an integer (index) or a string (column name).")

    def add_column(self, column: ColumnInfo):
        """新增一个列"""
        self.append(column)

    def remove_column(self, column_name: str):
        """通过字段英文名移除列"""
        for column in self:
            if column.name_en == column_name:
                self.remove(column)
                return
        raise KeyError(f"Column with name_en '{column_name}' not found.")


class TableInfo(BaseModel):
    """定义数据表的数据结构,仅用于生成SQL"""
    name_en: str  # 表英文名
    name_cn: Optional[str] = None  # 表中文名
    columns: ColumnManager = Field(default_factory=lambda: ColumnManager([]))  # 默认值为 ColumnManager 实例
    ddl: Optional[str] = None  # 数据表的 Data Definition Language
    description: Optional[str] = None  # 数据表的描述信息（240921新增）
    access_info: Optional[AccessInfo] = None  # 权限控制信息，用于后续鉴权 20250112新增

    model_config = ConfigDict(arbitrary_types_allowed=True)

    @model_validator(mode="before")
    @classmethod
    def validate_columns(cls, values):
        """
        自动将传入的 list 转换为 ColumnManager
        """
        if "columns" in values and isinstance(values["columns"], list):
            values["columns"] = ColumnManager(values["columns"])
        return values

    def __eq__(self, other):
        if isinstance(other, TableInfo):
            return self.name_en == other.name_en
        return False

    def __hash__(self):
        """用于后续去重 List(set())"""
        return hash(self.name_en + self.name_cn if self.name_cn else self.name_en)

    def __getitem__(self, key: str) -> ColumnInfo:
        """
        支持通过列名访问 ColumnInfo。
        """
        return self.columns[key]


class DataSourceException(Exception):
    pass


class TableManager(list):
    def __init__(self, tables: List[TableInfo]):
        super().__init__(tables)

    def __getitem__(self, key: Union[int, str, slice]) -> Union[TableInfo, List[TableInfo]]:
        """支持通过索引、切片或表英文名访问表"""
        if isinstance(key, int):
            return super().__getitem__(key)
        elif isinstance(key, str):
            for table in self:
                if table.name_en == key:
                    return table
            raise KeyError(f"Table with name_en '{key}' not found.")
        elif isinstance(key, slice):
            return super().__getitem__(key)
        else:
            raise TypeError("Key must be an integer (index), a string (table name), or a slice.")

    def __setitem__(self, key: Union[int, str], value: TableInfo):
        """支持通过索引或表英文名更新表"""
        if isinstance(key, int):
            super().__setitem__(key, value)
        elif isinstance(key, str):
            for idx, table in enumerate(self):
                if table.name_en == key:
                    super().__setitem__(idx, value)
                    return
            raise KeyError(f"Table with name_en '{key}' not found.")
        else:
            raise TypeError("Key must be an integer (index) or a string (table name).")

    def add_table(self, table: TableInfo):
        """新增一个表"""
        self.append(table)

    def remove_table(self, table_name: str):
        """通过表名移除表"""
        for table in self:
            if table.name_en == table_name:
                self.remove(table)
                return
        raise KeyError(f"Table with name_en '{table_name}' not found.")


class DataSource(ABC):
    type: Optional[str] = Field(None, description='数据源的类型（schema/no-schema）')
    database: Optional[str] = Field(None, description='数据库名称(sqlite, Mysql, SQL Server等)')

    def __init__(self,
                 source: str,  # source名不能为空
                 language: str = 'cn',
                 source_description: str = None,
                 es_client: ElasticsearchClient = None,
                 embedding_model: EmbeddingModel = None, ):

        if not source:
            raise DataSourceException("Source name cannot be empty")

        self.es_client: ElasticsearchClient = es_client  # ElasticSearch Client
        self.es_config: dict = self.es_client.export_config() if self.es_client else {}

        self.embedding_model: EmbeddingModel = embedding_model  # Embedding Model

        self.temp_manager = TempFileManager()  # 临时文件管理器

        self.source = source
        self.source_description: str = source_description  # 该source的描述
        self.language = language  # 设置语言，默认为中文语境下的问答

        # 配置一个枚举值管理器
        self.enum_manager: Optional[Enumeration] = None
        self.local_enums = None

        # 在数据源初始化的时候，自动配置一个外挂知识库
        self.knowledge: Optional[ExtraKnowledge] = None  # 外挂知识库

        # 初始化与ES、Embedding有关的属性
        self._initialize_knowledge(es_client=self.es_client, embedding_model=self.embedding_model)

        # 存放接入数据表(以TableInfo的形式)
        self.tables = TableManager([])

        # 用于适配原有的chatbi工作流，主要就是三个属性，后期可以废除 infos dfs
        self.infos: List[pd.DataFrame] = []  # 存放固定格式的表中文名、表英文名、字段英文名、字段中文、字段类型等信息
        self.dfs: List[pd.DataFrame] = []  # 存放数据表的真实数据

        self.metadata = None  # 存放数据的原数据

        self._initialize()

        self.data_sources: List['DataSource'] = [self]  # 用于存放append的ds

        printf(color='green', title='数据加载完成',
               message=f'表中文名:{self.get_table_names_cn()}\n表英文名:{self.get_table_names_en()}')

    def _initialize_knowledge(self,
                              es_client: ElasticsearchClient = None,
                              embedding_model: EmbeddingModel = None) -> None:
        """
        初始化与ElasticSearch、Embedding有关的属性，方便后续反序列化时重构
        Args:
            es_client:
            embedding_model:
        Returns:
        """

        self.es_client = es_client
        self.embedding_model = embedding_model

        # 配置枚举值管理器
        self.enum_manager: Enumeration = Enumeration(source=self.source, es_client=es_client)

        # 配置知识库
        # if self.es_client and self.embedding_model:
        #     try:
        #         self.knowledge = ExtraKnowledge(es_client=self.es_client,
        #                                         embedding_model=self.embedding_model,
        #                                         source=self.source)
        #
        #         printf(color='green', title='DataSource外挂知识库成功')
        #     except Exception as e:
        #         printf(color='yellow', title='DataSource外挂知识库失败', message=f"{e}")

        if self.embedding_model:
            try:
                self.knowledge = ExtraKnowledge(es_client=self.es_client,
                                                embedding_model=self.embedding_model,
                                                source=self.source)

                printf(color='green', title='DataSource外挂知识库成功')
            except Exception as e:
                printf(color='yellow', title='DataSource外挂知识库失败', message=f"{e}")

        self.es_config = self.es_client.export_config() if self.es_client else {}
        pass

    def reinitialize_enum_manager(self, es_client: ElasticsearchClient) -> None:
        """
        重新初始化Enum管理器，加上ES Client
        Args:
            es_client: ES Client
        Returns: None
        """
        if es_client and isinstance(es_client, ElasticsearchClient):
            self.enum_manager = Enumeration(source=self.source,
                                            es_client=es_client)
        pass

    def _initialize(self):
        """初始化数据和检查流程"""
        self.initialize_data()  # 初始化的时候把上面这些变量全都加载进去
        self._check_initialize()  # 检查是否正确被初始化
        self._process_language()  # 根据语言设置，处理 tables 中的数据，如果非中文，则删除中文相关字段；

    def _process_language(self):
        if self.language not in ['en', 'cn']:
            raise ValueError("语言参数设置必须为cn（中文）或en（英文）, language='cn' / language='en'")

        if self.language != 'cn':
            for table in self.tables:
                table.name_cn = None
                for column in table.columns:
                    column.name_cn = None

    def _transfer_tables_into_dfs_infos(self) -> None:
        """内部函数，将 TableInfo 转换为 self.infos 和 self.dfs"""
        self.infos = []
        self.dfs = []
        for table in self.tables:
            info_data = []
            for column in table.columns:
                info_data.append({
                    '表英文名': table.name_en,
                    '表中文名': getattr(table, 'name_cn', None),  # 安全获取 name_cn 属性
                    '字段英文名': column.name_en,
                    '字段中文名': getattr(column, 'name_cn', None),  # 安全获取 name_cn 属性
                    '字段类型': column.col_type,
                    '字段注释': column.col_comment
                })
            info_df = pd.DataFrame(info_data)
            self.infos.append(info_df)  # 获取信息数据 infos
            dfs = column_info_to_dataframe(table.columns)  # 获取样例数据 dfs
            self.dfs.append(dfs)
        return

    def _check_initialize(self):
        """
        检查 `self.tables` 是否被更新。如果 `self.tables` 为空，则抛出异常。
        """
        if not self.tables:
            raise RuntimeError(
                "`self.tables` 未被初始化。请确保子类实现了 `initialize_data` 方法并正确填充 `self.tables`。")
        if not isinstance(self.tables[0], TableInfo):
            raise RuntimeError("`self.tables` 中的元素必须为 `TableInfo` 类型。请检查子类的 `initialize_data` 实现。")

    @staticmethod
    def _generate_ddl_with_table_info(table_info: TableInfo) -> str:
        """
        根据 TableInfo 生成DDL语句。
        Args:
            table_info (TableInfo): 包含表和列信息的 TableInfo 对象。
        Returns:
            str: 生成的 DDL 语句。
        """
        # 开始生成 CREATE TABLE 语句
        ddl_lines = [f"CREATE TABLE {table_info.name_en} ("]

        # 添加表的中文名和注释
        if table_info.name_cn:
            ddl_lines.append(f"    -- {{\"表中文名\": \"{table_info.name_cn}\"}}")

        # 遍历列信息，生成每一列的定义
        for column in table_info.columns:
            column_line = f"    {column.name_en} {column.col_type}"

            # 添加字段的中文名和注释
            comments = []
            if column.name_cn:
                comments.append(f"\"字段中文\": \"{column.name_cn}\"")
            if column.col_comment:
                comments.append(f"\"注释\": \"{column.col_comment}\"")

            if comments:
                column_line += f", -- {{{', '.join(comments)}}}"
            else:
                column_line += ","

            ddl_lines.append(column_line)

        # 移除最后一行的逗号并添加右括号和结束符号
        if ddl_lines[-1].endswith(','):
            ddl_lines[-1] = ddl_lines[-1][:-1]  # 去掉最后一行的逗号
        ddl_lines.append(");")

        return "\n".join(ddl_lines)

    @abstractmethod
    def initialize_data(self):
        """用于将连接的数据源，转换为TableInfo，然后传给self.tables"""
        raise NotImplementedError("必须在子类中实现`initialize_data`方法")

    # @make_sync
    # async def execute(self, sql: str = None) -> Optional[pd.DataFrame]:
    #     # 同步版本，向后兼容异步execute
    #     return await self.async_execute(sql)

    @abstractmethod
    def execute(self, sql: str = None) -> Optional[pd.DataFrame]:
        raise NotImplementedError("execute is not implemented for this data source")

    async def async_execute(self, sql: str):
        # 异步查询数据库
        raise NotImplementedError("execute is not implemented for this data source")

    def append(self, data_source: 'DataSource'):
        """用于添加数据源"""
        if not isinstance(data_source, DataSource):
            raise ValueError('接入的数据必须是DataSource')

        if data_source.language != self.language:
            raise ValueError(
                f'接入的数据语言必须一直，接入语言为{data_source.language}，然而本实例所设置的语言{self.language}')
        self.tables.extend(data_source.tables)
        self._transfer_tables_into_dfs_infos()
        self.data_sources.append(data_source)

    def enhance_date_detection(self) -> Dict[str, List[Dict[str, Any]]]:
        """
        只需要在各个DBMS构造函数后面加上这一段就行
        """
        enhancer = DateDetectionEnhancer(self)
        results = enhancer.enhance_date_information()

        # Log detection results
        # if results:
        #     printf(color='green', title='Date Detection Results')
        #     for table_name, table_results in results.items():
        #         printf(color='blue', title=f'Table: {table_name}')
        #         for result in table_results:
        #             printf(
        #                 title='Date pattern detected',
        #                 color='green',
        #                 message=(
        #                     f"Column: {result['column_name']}\n"
        #                     f"Detection Method: {result['detection_method']}\n"
        #                     f"Detected Format: {result['detected_format']}\n"
        #                     f"Pattern Type: {result['pattern_type']}"
        #                 )
        #             )
        # else:
        #     printf(color='yellow', title='No date patterns detected')

        return results

    def add_protect_columns(self, protect_columns: List[str] = None):
        """
        添加保护列，在召回时不被排除掉
        不区分中英文！
        Args:
            protect_columns: List[str], 受保护的列名，例如 USER_ID，PROV等必须的字段
        Returns: None，仅改变数据结构
        """
        protect_columns = [p.upper() for p in protect_columns]
        if not protect_columns:
            printf(color='red', title="未指定保护列或保护列参数无效。", message=f'输入的保护列:{protect_columns}')
            return
        for table in self.tables:
            for column in table.columns:
                # 获取中文名，如果不存在则使用空字符串
                column_name_cn = getattr(column, 'name_cn', '').upper()
                table_name_cn = getattr(table, 'name_cn', '')
                # 在比较时，确保属性存在，避免 AttributeError
                if column.name_en.upper() in protect_columns or column_name_cn in protect_columns:
                    column.is_required = True
                    printf(color='green', title="保护列已设置。",
                           message=f'列名: {column_name_cn or column.name_en} - 对应表: {table_name_cn or table.name_en}')
                else:
                    pass

    def adaptive_schema(self,
                        query: str = None,
                        target_table: str = None) -> str:
        """
        用于生成自适应数据结构表达，根据传入的关键词列表匹配最相关的枚举值并包含在输出中。
        Args:
            query: str 用户的问题
            target_table (str, optional): 表名，用于指定生成某个表的 schema。如果为 None，则遍历所有表。
        Returns:
            str: 格式化的 schema 字符串。
        """
        generator = AdaptiveSchemaGenerator(data_source=self)
        ada_schema = generator.generate_schema(query=query, target_table=target_table)
        return ada_schema

    def generate_ddls(self) -> List[str]:
        """
        生成所有表的 DDL 语句,注意是生成!

        Returns:
            List[str]: 所有生成的 DDL 语句。
        """
        ddl_statements = []
        for table in self.tables:
            lines = []
            table_name = table.name_en
            lines.append(f"CREATE TABLE `{table_name}` (")
            column_definitions = []
            foreign_keys = []
            for column in table.columns:
                col_def = f"  `{column.name_en}` {column.col_type}"
                if not column.is_required:
                    col_def += " NULL"
                else:
                    col_def += " NOT NULL"
                if column.col_comment:
                    col_def += f" COMMENT '{column.col_comment}'"
                column_definitions.append(col_def)

                if column.is_foreign and column.ref_table and column.ref_column:
                    fk = f"  FOREIGN KEY (`{column.name_en}`) REFERENCES `{column.ref_table}`(`{column.ref_column}`)"
                    foreign_keys.append(fk)

            all_definitions = column_definitions + foreign_keys
            lines.append(",\n".join(all_definitions))
            lines.append(") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;")

            if table.description:
                lines[-1] = lines[-1][:-1] + f" COMMENT='{table.description}';"

            ddl = "\n".join(lines)
            ddl_statements.append(ddl)

        return ddl_statements

    def extract_foreign_keys(self) -> List[str]:
        """
        获取数据表的外键关联信息；
        Returns: List[str]
        """
        foreign_keys = []
        for table in self.tables:
            # 获取表名
            table_name = table.name_en
            for column in table.columns:
                if column.is_foreign:
                    # 获取列名
                    column_name = column.name_en
                    # 获取引用的表名和列名
                    if column.ref_table and column.ref_column:
                        # 查找引用的表信息
                        ref_table_info = next((t for t in self.tables if t.name_en == column.ref_table), None)
                        if ref_table_info:
                            ref_table_name = ref_table_info.name_en
                            # 查找引用的列信息
                            ref_column_info = next((col for col in ref_table_info.columns
                                                    if col.name_en == column.ref_column), None)
                            if ref_column_info:
                                ref_column_name = ref_column_info.name_en
                            else:
                                ref_column_name = column.ref_column
                        else:
                            ref_table_name = column.ref_table
                            ref_column_name = column.ref_column
                    else:
                        # 如果没有提供引用信息
                        ref_table_name = 'UNKNOWN_TABLE'
                        ref_column_name = 'UNKNOWN_COLUMN'

                    # 添加外键关系到列表
                    foreign_keys.append(f"{table_name}.{column_name} = {ref_table_name}.{ref_column_name}")
        return foreign_keys

    def filter_tables(self, table_names: List[str], fuzzy: bool = False) -> 'DataSource':
        """
        根据指定的表名列表，创建一个新的 DataSource 实例，仅包含所选的表。
        不区分中英文表名！

        Args:
            table_names (List[str]): 要选择的表名列表。
            fuzzy: 是否模糊匹配表的名称
        Returns:
            DataSource: 包含所选表的新 DataSource 实例。
        """
        new_ds = copy.copy(self)
        if not fuzzy:
            temp_tables = [table for table in new_ds.tables if table.name_en in table_names
                           or table.name_cn in table_names]

            # 如果table中有受保护列，也不能被去掉
            protect_table = []
            for table in new_ds.tables:
                for column in table.columns:
                    if column.is_required is True:
                        protect_table.append(table)

            # 更新新实例的 tables 属性为选中的表

            new_ds.tables = list(set(temp_tables + protect_table))

            # 重新生成 infos 和 dfs 以确保数据一致
            new_ds._transfer_tables_into_dfs_infos()
            return new_ds
        else:
            candidates_tables = [table.name_cn for table in new_ds.tables] + [table.name_en for table in new_ds.tables]
            matched = []
            for tb in table_names:
                most_similar = SubStringSimilarity().find_best_match(target=tb, candidates=candidates_tables, top_n=1)[0]
                matched.append(most_similar)

            temp_tables = [table for table in new_ds.tables if table.name_en in matched
                           or table.name_cn in matched]

            # 更新新实例的 tables 属性为选中的表
            new_ds.tables = temp_tables
            # 重新生成 infos 和 dfs 以确保数据一致
            new_ds._transfer_tables_into_dfs_infos()
            return new_ds

    def is_empty(self) -> bool:
        """
        判断ds是否为空
        Returns: bool
        """
        if not self.tables:
            return True
        else:
            return False

    def filter_columns_with_retrieved_table_and_columns(self,
                                                        retrieved: List[Dict]) -> 'DataSource':
        """
        输入recall 的输出

        [{'table_name': 'DTS_YTD_EMPL_EXIT_DTL_VW',
         'fields': ['ACTION_DESCR', 'SCHOOL_DESCR', 'LV3_ORG_NM', 'EFFDT']},

        {'table_name': 'DTS_YTD_EMPL_ENTRY_DTL_VW',
         'fields': ['ACTION_DESCR', 'ACTION_REASON_DESCR', 'C_KEY_INSTI', 'DEPT_NM', 'EFFDT', 'EMPL_ID']}]

        Returns: DataSource
        """
        # 创建一个新的 DataSource 实例作为返回
        new_ds = copy.copy(self)
        new_ds.tables = []  # 初始化为一个空列表，以便重新添加筛选后的表

        # 遍历当前数据源中的每个表
        for table in self.tables:
            for retrieved_table in retrieved:
                if table.name_en != retrieved_table['table_name']:
                    continue

                filtered_columns = [
                    column for column in table.columns
                    if column.name_en in retrieved_table['fields']
                       or column.is_required
                       or column.name_cn in retrieved_table['fields']
                ]

                if filtered_columns:
                    # 创建一个新的表信息，包含筛选后的列
                    filtered_table = TableInfo(
                        name_en=table.name_en,
                        name_cn=table.name_cn,
                        columns=filtered_columns,
                        ddl=''
                    )

                    filtered_table.ddl = self._generate_ddl_with_table_info(filtered_table)
                    new_ds.tables.append(filtered_table)

        new_ds._transfer_tables_into_dfs_infos()
        printf(color='green', title="列过滤已完成，生成了仅包含所选列和保护列的新数据源。",
               message=f'过滤后的表: {new_ds.get_table_names_en()}, 数据列: {new_ds.get_column_cn_names()}')
        return new_ds

    def filter_columns(self,
                       select_columns: List[str],
                       protect_columns: List[str] = None) -> 'DataSource':
        """
        根据指定的列名列表，找到对应的数据表，并返回一个仅包含了筛选列及保护列的实例。
        （主要用于ChatBI提速，减少不必要的列，加快推理速度）
        字段名不区分中英文
        Args:
            select_columns (List[str]): 要选择的列名列表。
            protect_columns(List[str]): 受保护的列名列表。例如USER_ID, PROV, STATIS_YM等
        Returns:
            DataSource: 仅包含筛选列及保护列的新 DataSource 实例。
        """
        # 添加保护白名单
        if protect_columns:
            self.add_protect_columns(protect_columns=protect_columns)

        # 创建一个新的 DataSource 实例作为返回
        new_ds = copy.copy(self)
        new_ds.tables = []  # 初始化为一个空列表，以便重新添加筛选后的表

        # 遍历当前数据源中的每个表
        for table in self.tables:
            # 筛选出表中的列，保留需要的列和受保护的列
            filtered_columns = [
                column for column in table.columns
                if column.name_en in select_columns
                   or column.is_required
                   or column.name_cn in select_columns
            ]

            if filtered_columns:
                # 创建一个新的表信息，包含筛选后的列
                filtered_table = TableInfo(
                    name_en=table.name_en,
                    name_cn=table.name_cn,
                    columns=filtered_columns,
                    ddl=''
                )

                filtered_table.ddl = self._generate_ddl_with_table_info(filtered_table)
                new_ds.tables.append(filtered_table)

        new_ds._transfer_tables_into_dfs_infos()
        printf(color='green', title="列过滤已完成，生成了仅包含所选列和保护列的新数据源。",
               message=f'过滤后的表: {new_ds.get_table_names_en()}, 数据列: {new_ds.get_column_cn_names()}')
        return new_ds

    def get_ddls(self, table_name=None):
        """
        获取数据表的建表语句
        Returns:
            Dict['表英文名', '该表的DDL']
        """
        table_name_tmp = table_name
        if table_name_tmp and table_name not in self.get_table_names_en():
            return None

        ddls_dict = {}
        for table in self.tables:
            table_name = table.name_en
            table_ddl = table.ddl
            ddls_dict[table_name] = table_ddl
        if table_name_tmp:
            return ddls_dict[table_name_tmp]
        return ddls_dict

    def get_table_names_en(self) -> List[str]:
        """
        获取DataSource中存放的数据表名信息（英文表名）
        Returns: List[数据表名（英文名）]
        """
        table_names_en = []
        for table in self.tables:
            if table.name_en:
                table_names_en.append(table.name_en)
        return table_names_en

    def get_table_names_cn(self) -> List[str]:
        """
        获取DataSource中存放的数据表名信息(中文名)
        Returns: List[数据表名（中文名）]
        """
        if self.language == 'cn':
            table_names_cn = []
            for table in self.tables:
                if table.name_cn:
                    table_names_cn.append(table.name_cn)
            return table_names_cn
        else:
            return []

    def find_tables_by_column(self, column_name: str) -> List[str]:
        """
        查找包含特定字段的表。
        Args:
            column_name (str): 字段的英文名
        Returns:
            List[str]: 包含该字段的表名列表
        """
        tables_with_column = []
        for table in self.tables:
            if any(col.name_en == column_name for col in table.columns):
                tables_with_column.append(table.name_en)
        return tables_with_column

    def display_table_info(self, table_name: str) -> None:
        """
        打印指定表的完整信息，包括表名、字段信息和DDL。
        Args:
            table_name (str): 数据表的英文名。
        """
        table = next((t for t in self.tables if t.name_en == table_name), None)
        if not table:
            print(f"表 {table_name} 不存在于数据源中。")
            return

        print(f"表英文名: {table.name_en}")
        if self.language == 'cn':
            print(f"表中文名: {table.name_cn}")
        print(f"DDL: {table.ddl}")
        print("字段信息:")
        for column in table.columns:
            print(f"  - 字段英文名: {column.name_en}")
            if self.language == 'cn':
                print(f"    字段中文名: {column.name_cn}")
            print(f"    字段类型: {column.col_type}")
            print(f"    字段注释: {column.col_comment}")
            print(f"    示例数据: {column.example_data}")
            print(
                f"    枚举值: {self.enum_manager.get_enums_by_column(table_name_en=table.name_en, column_name_en=column.name_en)}")

    def find_tables_by_value(self, value: Any) -> Optional[Tuple[TableInfo, ColumnInfo, Any]]:
        """
        根据字段的示例数据或枚举值查找包含特定值的表，并返回包含该值的表名和字段信息。

        Args:
            value (Any): 要查找的值。

        Returns:
            List[Tuple[str, ColumnInfo]]: 包含该值的表名和字段信息的列表。
        """
        for table in self.tables:
            for column in table.columns:
                # 检查示例数据和枚举值是否包含指定的值
                col_enums: List[str] = self.enum_manager.get_enums_by_column(table_name_en=table.name_en,
                                                                             column_name_en=column.name_en)
                if (column.example_data and value in column.example_data) or (col_enums and value in col_enums):
                    return table, column, value
        return None

    def validate_data_integrity(self) -> None:
        """
        验证 `self.tables` 中的数据完整性和一致性，确保表和字段的信息正确。
        """
        if not self.tables:
            raise ValueError("没有可用的表信息。")

        for table in self.tables:
            if not isinstance(table, TableInfo):
                raise TypeError(f"表 {table.name_en} 的数据类型错误，应为 TableInfo 类型。")

            for column in table.columns:
                if not isinstance(column, ColumnInfo):
                    raise TypeError(f"表 {table.name_en} 中字段 {column.name_en} 的数据类型错误，应为 ColumnInfo 类型。")

        print("数据完整性和一致性检查通过。")

    def export_to_json(self, filepath: str) -> None:
        """
        导出数据源的表信息为 JSON 文件。
        Args:
            filepath (str): 输出文件路径。
        """
        import json
        data = [table.model_dump() for table in self.tables]
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=4)
        print(f"表信息已导出到 {filepath}")

    def find_tables_by_value_fuzzy(self, value: Any,
                                   threshold: float = 0.7) -> Optional[List[Tuple[TableInfo, ColumnInfo, Any]]]:
        """
        根据字段的示例数据或枚举值模糊匹配查找包含特定值的表，并返回包含该值的表名、字段信息和最匹配的值。

        Args:
            value (Any): 要查找的值。
            threshold (float): 模糊匹配的相似度阈值，数值越高要求匹配越精确。

        Returns:
            List[Tuple[TableInfo, ColumnInfo, Any]]: 包含模糊匹配到该值的表信息、字段信息和最匹配的值的列表。
        """
        from difflib import SequenceMatcher

        def similarity(a: str, b: str) -> float:
            """计算两个字符串的相似度。"""
            return SequenceMatcher(None, a, b).ratio()

        value_str = str(value)  # 将搜索值转换为字符串进行匹配
        record = []

        for table in self.tables:
            for column in table.columns:
                best_match = None
                best_similarity = 0.0
                col_enums: List[str] = self.enum_manager.get_enums_by_column(table_name_en=table.name_en,
                                                                             column_name_en=column.name_en)
                # 检查枚举值
                if col_enums:
                    for enum_value in col_enums:
                        enum_value_str = str(enum_value)
                        sim = similarity(value_str, enum_value_str)
                        if sim >= threshold and sim > best_similarity:
                            best_similarity = sim
                            best_match = enum_value

                # 如果找到最匹配的值且相似度大于等于阈值，则记录该字段
                if best_match is not None:
                    record.append((table, column, best_match))
        return record

    def find_tables_by_value_fuzzy_fast(self,
                                        value: Any,
                                        threshold: float = 0.7,
                                        method: str = 'seq') -> Optional[Tuple[TableInfo, ColumnInfo, Any]]:
        """
        根据字段的示例数据或枚举值模糊匹配查找包含特定值的表，并返回包含该值的表名和字段信息。
        快速版本，找到匹配的数据便返回。
        Args:
            value (Any): 要查找的值。
            threshold (float): 模糊匹配的相似度阈值，默认是0.7，数值越高要求匹配越精确。
            method (Str): 模糊匹配的方法
        Returns:
            List[Tuple[str, ColumnInfo]]: 包含模糊匹配到该值的表名和字段信息的列表。
        """

        if method not in ['jac', 'lev', 'seq']:
            raise ValueError('method must be "jac", "lev", "seq')

        similarity_func = {'jac': JaccardSimilarity(),
                           'lev': LevenshteinSimilarity(),
                           'seq': SubStringSimilarity()}

        def is_fuzzy_match(a: str, b: str, thread: float) -> bool:
            """判断两个字符串是否模糊匹配，依据相似度阈值。"""
            return similarity_func.get(method).get_similarity_score(a, b) >= thread

        value_str = str(value)  # 将搜索值转换为字符串进行匹配

        for table in self.tables:
            for column in table.columns:
                col_enums: List[str] = self.enum_manager.get_enums_by_column(table_name_en=table.name_en,
                                                                             column_name_en=column.name_en)
                if col_enums:  # 查找枚举值，找到最相似的便返回
                    for enum_value in col_enums:
                        if is_fuzzy_match(value_str, str(enum_value), threshold):
                            return table, column, str(enum_value)
        return None

    def global_fuzzy_search(self,
                            query: str,
                            threshold: float = 0.5,
                            method: str = 'seq',
                            max_results: int = 10) -> Dict[str, List[Dict[str, Any]]]:
        """
        全局模糊搜索，搜索范围包括表名、字段名和枚举值

        Args:
            query (str): 搜索关键词
            threshold (float): 匹配阈值，默认0.5
            method (str): 匹配方法，可选 'jac'(Jaccard), 'lev'(Levenshtein), 'dice'(Dice), 'seq'(SequenceMatcher)
            max_results (int): 每个类别返回的最大结果数量

        Returns:
            Dict[str, List[Dict[str, Any]]]: 搜索结果，格式为：
            {
                'tables': [
                    {
                        'table_name_en': str,
                        'table_name_cn': str,
                        'similarity': float
                    }
                ],
                'columns': [
                    {
                        'table_name_en': str,
                        'table_name_cn': str,
                        'column_name_en': str,
                        'column_name_cn': str,
                        'similarity': float
                    }
                ],
                'enums': [
                    {
                        'table_name_en': str,
                        'column_name_en': str,
                        'enum_value': str,
                        'similarity': float
                    }
                ]
            }
        """
        if not query:
            return {
                'tables': [],
                'columns': [],
                'enums': []
            }

        # 验证匹配方法
        if method not in ['jac', 'lev', 'seq']:
            raise ValueError('method must be one of: jac, lev, seq')

        similarity_func = {
            'jac': JaccardSimilarity(),
            'lev': LevenshteinSimilarity(),
            'seq': SubStringSimilarity()
        }.get(method)

        results = {
            'tables': [],
            'columns': [],
            'enums': []
        }

        # 1. 搜索表名
        table_matches = []
        for table in self.tables:
            # 计算英文名相似度
            en_similarity = similarity_func.get_similarity_score(query, table.name_en)
            # 如果有中文名，计算中文名相似度
            cn_similarity = similarity_func.get_similarity_score(query, table.name_cn) if table.name_cn else 0
            # 取最高相似度
            max_similarity = max(en_similarity, cn_similarity)

            if max_similarity >= threshold:
                table_matches.append({
                    'table_name_en': table.name_en,
                    'table_name_cn': table.name_cn,
                    'similarity': max_similarity
                })

        # 2. 搜索字段名
        column_matches = []
        for table in self.tables:
            for column in table.columns:
                # 计算英文名相似度
                en_similarity = similarity_func.get_similarity_score(query, column.name_en)
                # 如果有中文名，计算中文名相似度
                cn_similarity = similarity_func.get_similarity_score(query, column.name_cn) if column.name_cn else 0
                # 取最高相似度
                max_similarity = max(en_similarity, cn_similarity)

                if max_similarity >= threshold:
                    column_matches.append({
                        'table_name_en': table.name_en,
                        'table_name_cn': table.name_cn,
                        'column_name_en': column.name_en,
                        'column_name_cn': column.name_cn,
                        'similarity': max_similarity
                    })

        # 3. 搜索枚举值
        enum_matches = []
        for table in self.tables:
            for column in table.columns:
                # 获取该字段的枚举值
                enums = self.enum_manager.get_enums_by_column(table.name_en, column.name_en)
                if not enums:
                    continue

                for enum_value in enums:
                    if not isinstance(enum_value, str):
                        continue

                    similarity = similarity_func.get_similarity_score(query, enum_value)
                    if similarity >= threshold:
                        enum_matches.append({
                            'table_name_en': table.name_en,
                            'column_name_en': column.name_en,
                            'enum_value': enum_value,
                            'similarity': similarity
                        })

        # 对每个类别的结果按相似度排序并限制数量
        results['tables'] = sorted(table_matches, key=lambda x: x['similarity'], reverse=True)[:max_results]
        results['columns'] = sorted(column_matches, key=lambda x: x['similarity'], reverse=True)[:max_results]
        results['enums'] = sorted(enum_matches, key=lambda x: x['similarity'], reverse=True)[:max_results]

        return results

    def match_columns_fuzzy(self,
                            target: List[str],
                            threshold: float = 0.5,
                            method: str = 'seq') -> List[str]:

        """
        本方法将问题中的关键词进行枚举值和列名的检索，例如：“苏州有多少网格经理办理过宽带业务” -> ['苏州', '网格经理', '宽带业务']
        然后将这些关键词与数据表中的字段名（中文）与枚举值进行比对，最终返回符合这些数据的列名。

        与 match_column_name_en 方法不一样，match_column_name_en 是基于target为列名的假设，仅检索列名；
        match_column是根据target为列名或者数据值的假设，检索枚举值和列名；然后用于设置保护列；
        Args:
            target (Str): 问题中的关键词，可以是column name也可以是 value
            threshold (float): 模糊匹配的相似度阈值，默认是0.7，数值越高要求匹配越精确。
            method (Str): 模糊匹配的方法
        Returns:
            List[str]: 匹配到的 column_en
        """
        matched_cols = []

        if method not in ['jac', 'lev', 'seq']:
            raise ValueError('method must be "jac", "lev", "dice", "seq"')

        similarity_func = {'jac': JaccardSimilarity(),
                           'lev': LevenshteinSimilarity(),
                           'seq': SubStringSimilarity()}

        def is_fuzzy_match(a: str, b: str, thread: float) -> bool:
            return similarity_func.get(method).get_similarity_score(a, b) >= thread

        for value_str in target:
            value_str = str(value_str)
            for table in self.tables:
                for column in table.columns:
                    col_enums: List[str] = self.enum_manager.get_enums_by_column(table_name_en=table.name_en,
                                                                                 column_name_en=column.name_en)
                    if hasattr(column, 'name_cn'):
                        if is_fuzzy_match(value_str, column.name_cn, threshold):
                            matched_cols.append(column.name_en)
                    if col_enums:
                        for enum_value in col_enums:
                            if is_fuzzy_match(value_str, str(enum_value), threshold):
                                matched_cols.append(column.name_en)

        return list(set(matched_cols))

    def get_column_enums(self,
                         table_name: str,
                         column_name: str,
                         force_es: bool = False) -> List[Any]:
        """
        获取指定表和字段的枚举值。
        Args:
            table_name (str): 表的英文名。
            column_name (str): 字段的英文名。
            force_es( bool): 强制从es中取数
        Returns:
            List[Any]: 枚举值列表，如果找不到表或字段，返回空列表。
        """
        # 查找表
        table = next((t for t in self.tables if t.name_en == table_name), None)
        if not table:
            print(f"表 {table_name} 不存在。3")
            return []

        # 查找字段
        column = next((col for col in table.columns if col.name_en == column_name), None)
        if not column:
            print(f"字段 {column_name} 不存在于表 {table_name} 中。")
            return []

        col_enums: List[str] = self.enum_manager.get_enums_by_column(table_name_en=table.name_en,
                                                                     column_name_en=column.name_en)
        # 返回枚举值列表，去掉 None
        return [enum for enum in col_enums if enum is not None] if col_enums else []

    def get_column_en_names(self, table_name: Optional[str] = None) -> List[str]:
        """
        根据表名（表英文名或中文名）获取该表的所有字段名（英文）。如果未指定数据表名，则返回数据源所有的字段名
        Args:
            table_name (str): 表名，可以是英文名或中文名。

        Returns:
            List[str]: 字段英文名列表，如果找不到表，返回空列表。
        """
        if table_name is None:
            all_column_names = [column.name_en for table in self.tables for column in table.columns]
            return all_column_names

        # 查找表：根据表的英文名或中文名匹配
        table = next((t for t in self.tables if t.name_en == table_name or t.name_cn == table_name), None)
        if not table:
            print(f"表 {table_name} 不存在。2")
            return []

        # 提取所有字段的英文名
        column_names = [column.name_en for column in table.columns]
        return column_names

    def get_column_cn_names(self, table_name: Optional[str] = None) -> List[str]:
        """
        根据表名（表英文名或中文名）获取该表的所有字段名（中）。
        Args:
            table_name (str): 表名，可以是英文名或中文名。
        Returns:
            List[str]: 字段中文名列表，如果找不到表，返回空列表。
        """
        if table_name is None:
            all_column_names = [column.name_cn for table in self.tables for column in table.columns]
            return all_column_names

        # 查找表：根据表的英文名或中文名匹配
        table = next((t for t in self.tables if t.name_en == table_name or t.name_cn == table_name), None)
        if not table:
            print(f"表 {table_name} 不存在。1")
            return []

        # 提取所有字段的中文名
        column_names = [column.name_cn for column in table.columns]
        return column_names

    def match_table_name_en(self, table_name: str, method: str = 'seq') -> str:
        """
        匹配数据源中最相似的数据表英文名，用于SQL纠错
        Args:
            table_name:
            method: 计算相似度的方法，jaccard / Levenshtein / Dice
        Returns: str 最匹配的表名英文名
        """
        if method not in ['jac', 'lev', 'seq']:
            raise ValueError('method must be "jac", "lev", "seq"')

        similarity_func = {'jac': JaccardSimilarity(),
                           'lev': LevenshteinSimilarity(),
                           'seq': SubStringSimilarity()}

        cal_similarity = similarity_func.get(method, LevenshteinSimilarity())

        # 获取所有表的英文名
        table_names = self.get_table_names_en()

        # 初始化最相似表名和最高相似度
        best_match = ''
        highest_similarity = 0.0

        # 遍历所有表名，计算与输入表名的相似度
        for current_table in table_names:
            similarity = cal_similarity.get_similarity_score(table_name, current_table)

            # 更新最相似表名
            if similarity > highest_similarity:
                highest_similarity = similarity
                best_match = current_table

        return best_match

    def match_column_name_en(self, column_name: str, table_name: Optional[str] = None, method: str = 'jac') -> str:
        """
        匹配数据源中最相似的字段名，用于 SQL 纠错。

        Args:
            column_name (str): 待匹配的字段名。
            table_name (Optional[str]): 限定要查找的表名，如果为 None，则在数据源中所有字段中查找最相似的字段名。
            method (str): 匹配方法，可选 'jac'（Jaccard）、'lev'（Levenshtein）、'dice'（Dice）。

        Returns:
            str: 纠错后的最匹配字段名。
        """
        if method not in ['jac', 'lev', 'dice', 'seq']:
            raise ValueError('method must be "jac", "lev", "seq" or "dice"')

        # 相似度计算方法的映射
        similarity_func = {
            'jac': JaccardSimilarity(),
            'lev': LevenshteinSimilarity(),
            'seq': SubStringSimilarity()
        }

        cal_similarity = similarity_func.get(method, LevenshteinSimilarity())

        # 获取所有字段英文名，考虑是否限定在某个表内
        if table_name:
            # 查找指定表名的字段
            table = next((t for t in self.tables if t.name_en == table_name or t.name_cn == table_name), None)
            if not table:
                print(f"表 {table_name} 不存在。4")
                return ""
            column_names = [column.name_en for column in table.columns]
        else:
            # 查找所有表的字段
            column_names = [column.name_en for table in self.tables for column in table.columns]

        # 初始化最相似字段名和最高相似度
        best_match = ''
        highest_similarity = 0.0

        # 遍历所有字段名，计算与输入字段名的相似度
        for current_column in column_names:
            similarity = cal_similarity.get_similarity_score(column_name, current_column)

            # 更新最相似字段名
            if similarity > highest_similarity:
                highest_similarity = similarity
                best_match = current_column

        return best_match

    def match_enums(self,
                    table_name: str,
                    column_name: str,
                    value: str,
                    method: str = 'jac') -> Union[str, None]:
        """
        用于匹配枚举值，根据给定的表名、字段名和待匹配值，找到最相似的枚举值。
        当字段的枚举值为空的时候，最佳匹配返回None
        Args:
            table_name (str): 表的英文名或中文名。
            column_name (str): 字段的英文名。
            value (str): 待匹配的目标值。
            method (str): 匹配方法，可选 'jac'（Jaccard）、'lev'（Levenshtein）、'dice'（Dice）。默认使用 'jac'。
        Returns:
            str: 匹配到的最相似的枚举值。如果找不到合适的枚举值，则返回空字符串。
        """
        if method not in ['jac', 'lev', 'dice']:
            raise ValueError('method must be "jac", "lev", or "dice"')

        # 相似度计算方法的映射
        similarity_func = {
            'jac': JaccardSimilarity(),
            'lev': LevenshteinSimilarity()
        }

        # 选择相应的相似度计算函数，默认为 Levenshtein
        cal_similarity = similarity_func.get(method, LevenshteinSimilarity())

        # 获取指定表和字段的枚举值
        col_enums = self.get_column_enums(table_name=table_name, column_name=column_name)

        # 如果枚举值为空或 None，直接返回空字符串
        if not col_enums:
            print(f"字段 {column_name} 在表 {table_name} 中没有定义枚举值。")
            return None

        # 初始化最相似的枚举值和最高相似度
        best_match = None
        highest_similarity = 0.0

        # 遍历所有枚举值，计算与输入值的相似度
        for enum_value in col_enums:
            similarity = cal_similarity.get_similarity_score(value, str(enum_value))

            # 更新最相似的枚举值
            if similarity > highest_similarity:
                highest_similarity = similarity
                best_match = str(enum_value)

        return best_match

    @staticmethod
    def match_enums_with_keywords(enums: List[Any], keywords: List[str], threshold: float = 0.5) -> List[Any]:

        from difflib import SequenceMatcher

        matched_enums = set()
        for keyword in keywords:
            for enum in enums:
                # Calculate similarity between keyword and enum
                similarity = SequenceMatcher(None, str(keyword), str(enum)).ratio()
                if similarity >= threshold:
                    matched_enums.add(enum)
        return list(matched_enums)

    def export_table_infos(self, path: str = None) -> None:
        """
        将 self.tables 中的每个 TableInfo 导出为 Excel 文件 （可用于生成add_table_description的配置文件）
        每个文件中的列名为：
        ['original_column_name', 'column_name', 'column_description',
         'data_format', 'value_description', 'is_foreign', 'ref_table',
         'ref_column', 'is_datetime', 'datetime_pattern', 'alias']
        :param path: 保存文件的目录，默认为当前目录下的 'table_infos' 文件夹
        """
        if path is None:
            raise ValueError('请传入导出路径')

        if not os.path.exists(path):
            os.makedirs(path)

        # 遍历每个 TableInfo 并导出为单独的 Excel 文件
        for table in self.tables:
            data = []
            for column in table.columns:
                data.append({
                    'original_column_name': column.name_en,
                    'column_name': column.name_cn if self.language == 'cn' else column.name_en,
                    'column_description': column.col_comment,
                    'data_format': column.col_type,
                    'value_description': column.value_description,
                    'is_foreign': column.is_foreign,
                    'ref_table': column.ref_table,
                    'ref_column': column.ref_column,
                    'is_datetime': column.is_datetime,
                    'datetime_pattern': column.datetime_pattern,
                    'alias': column.alias
                })

            df = pd.DataFrame(data)
            file_path = os.path.join(path, f"{table.name_en}.xlsx")
            df.to_excel(file_path, index=False)
        printf(color='blue', title='导出成功', message=f'表信息已导出到目录: {path}')

    def get_table_detail_infos(
            self,
            table_name_en: Optional[str] = None,
            column_name_en: Optional[str] = None,
            show_enum: bool = False) -> Optional[Union[List[Dict[str, Any]], Dict[str, Any]]]:
        """
        根据输入参数逐级查询 DataSource 中的 tables 和 columns 信息

        参数:
            table_name_en (str, optional): 表的英文名。
            column_name_en (str, optional): 列的英文名。
            show_enum (bool): 控制是否显示完整的 enums。1 为显示全部，其他值仅显示前五个

        返回:
            - 如果仅提供 table_name_en，返回表的 name_en、name_cn、description 以及所有列的 name_en 和 name_cn。
            - 如果仅提供 column_name_en，提示需要提供 table_name_en。
            - 如果同时提供 table_name_en 和 column_name_en，返回对应列的所有 ColumnInfo 信息，并根据 show_enum 控制 enums 的显示。
            - 如果查询不到对应的表或列，返回 None。
        """
        if table_name_en and not column_name_en:
            # 查找表
            table = next((t for t in self.tables if t.name_en == table_name_en), None)
            if not table:
                printf(color='red', title='查询错误', message=f'表 "{table_name_en}" 未找到。')
                raise ValueError(f'表 "{table_name_en}" 未找到。')

            # 构建返回信息
            table_info = {
                "name_en": table.name_en,
                "name_cn": table.name_cn,
                "description": table.description,
                "columns": [{"name_en": col.name_en, "name_cn": col.name_cn} for col in table.columns],
                "access_info": table.access_info if isinstance(table.access_info, AccessInfo) else {},
            }
            return table_info

        elif column_name_en and not table_name_en:
            # 需要同时提供 table_name_en
            printf(color='red', title='输入错误', message='查询列信息需要同时提供表英文名')
            raise ValueError(f'查询列信息需要同时提供表英文名')

        elif table_name_en and column_name_en:
            # 查找对应的表
            table = next((t for t in self.tables if t.name_en == table_name_en), None)
            if not table:
                printf(color='red', title='查询错误', message=f'表 "{table_name_en}" 未找到。')
                raise ValueError(f'表 "{table_name_en}" 未找到。')

            # 查找对应的列
            column = next((c for c in table.columns if c.name_en == column_name_en), None)
            if not column:
                printf(color='red', title='查询错误', message=f'数据列 "{column_name_en}" 未找到。')
                raise ValueError(f'表 "{column_name_en}" 未找到。')

            # 构建 ColumnInfo 字典
            column_info = column.model_dump()
            access_info = column.access_info
            column_info['access_info'] = access_info
            col_enums: List[str] = self.enum_manager.get_enums_by_column(table_name_en=table.name_en,
                                                                         column_name_en=column.name_en)

            # 控制 enums 的显示
            if col_enums:
                if show_enum:
                    # 显示完整的 enums
                    column_info['enums'] = col_enums
                else:
                    # 仅显示前五个 enums
                    column_info['enums'] = col_enums[:5]
            return column_info

        else:
            # 如果没有提供任何参数，返回所有表的基本信息
            printf(color='red', title='查询错误', message=f'需提供表名或表名与字段名')
            raise ValueError(f'需提供表名或表名与字段名')

    ######################################################
    ##                                                  ##
    ##       修改 DataSource中的内容  2024.11.28          ##
    ##                                                  ##
    ######################################################

    def add_table(self, table_info: Dict[str, Any]) -> Dict[str, Any]:
        """
        添加一个新的数据表
        Args:
            table_info: 表信息的字典，必须包括至少 'name_en'
        Returns:操作结果及新增的表信息
        """
        try:
            access_info = table_info.get('access', None)

            if isinstance(access_info, dict):
                access_info = AccessInfo(permissions=access_info)
            else:
                access_info = None

            table = TableInfo(**table_info)
            table.access_info = access_info
            new_table = table

        except Exception as e:
            return {
                "success": False,
                "message": f"表信息验证失败: {e}",
                "updated_info": None
            }

        # 检查表是否已存在
        if new_table in self.tables:
            return {
                "success": False,
                "message": f'表 "{new_table.name_en}" 已存在。',
                "updated_info": None
            }

        self.tables.append(new_table)
        printf(color='green', title='添加成功', message=f'表 "{new_table.name_en}" 已成功添加。')
        return {
            "success": True,
            "message": f'表 "{new_table.name_en}" 已成功添加。',
            "updated_info": new_table.model_dump()
        }

    def delete_table(self, table_name_en: str) -> Dict[str, Any]:
        """
        删除指定的数据表
        Args:
            table_name_en: 要删除的表名
        Returns:

        """
        for idx, table in enumerate(self.tables):
            if table.name_en == table_name_en:
                del self.tables[idx]
                printf(color='green', title='删除成功', message=f'表 "{table_name_en}" 已成功删除。')
                return {
                    "success": True,
                    "message": f'表 "{table_name_en}" 已成功删除。',
                    "updated_info": None
                }

        return {
            "success": False,
            "message": f'表 "{table_name_en}" 未找到',
            "updated_info": None
        }

    def edit_table(self,
                   table_name_en: str,
                   updates: Dict[str, Any]) -> Dict[str, Any]:
        """
        编辑指定的数据表的信息
        参数:
            table_name_en (str): 表的英文名。
            updates (dict): 要更新的表字段及其新值。
                - 支持更新的字段包括: name_cn, description, ddl
        返回:
            dict: 操作结果及更新后的表信息。
                {
                    "success": bool,
                    "message": str,
                    "updated_info": dict  # 更新后的表信息
                }
        """
        if not updates:
            return {
                "success": False,
                "message": "更新内容不能为空。",
                "updated_info": None
            }

        # 查找表
        table = next((t for t in self.tables if t.name_en == table_name_en), None)
        if not table:
            return {
                "success": False,
                "message": f'表 "{table_name_en}" 未找到。',
                "updated_info": None
            }

        # 检查更新的字段是否合法
        allowed_fields = {"name_cn", "description", "ddl"}
        invalid_fields = [key for key in updates if key not in allowed_fields]

        if invalid_fields:
            return {
                "success": False,
                "message": f"无效的更新字段: {invalid_fields}。",
                "updated_info": None
            }

        # 更新
        for key, value in updates.items():
            if hasattr(table, key):
                setattr(table, key, value)

        printf(color='green', title='更新成功', message=f'表 "{table_name_en}" 信息已成功更新: {updates}')
        return {
            "success": True,
            "message": f'表 "{table_name_en}" 信息已成功更新。',
            "updated_info": table.model_dump()
        }

    def add_column(self,
                   table_name_en: str,
                   column_info: Dict[str, Any]) -> Dict[str, Any]:
        """
        向指定的表中添加一个新字段
        Args:
            table_name_en: 表英文名
            column_info: 包含字段信息的字典，必须包括至少 'name_en'
        Returns:操作结果及新增的字段信息
        """
        # 查找表
        table = next((t for t in self.tables if t.name_en == table_name_en), None)
        if not table:
            return {
                "success": False,
                "message": f'表 "{table_name_en}" 未找到。',
                "updated_info": None
            }

        try:
            access_info = column_info.get('access', None)

            if isinstance(access_info, dict):
                access_info = AccessInfo(permissions=access_info)
            else:
                access_info = None

            col_info = ColumnInfo(**column_info)
            col_info.access_info = access_info
            new_column = col_info

        except Exception as e:
            return {
                "success": False,
                "message": f"字段信息验证失败: {e}",
                "updated_info": None
            }

        # 检查字段是否已存在
        if new_column in table.columns:
            return {
                "success": False,
                "message": f'字段 "{new_column.name_en}" 已存在于表 "{table_name_en}" 中',
                "updated_info": None
            }

        table.columns.append(new_column)
        printf(color='green', title='添加成功', message=f'字段 "{new_column.name_en}" 已成功添加到表 "{table_name_en}"')
        return {
            "success": True,
            "message": f'字段 "{new_column.name_en}" 已成功添加到表 "{table_name_en}"。',
            "updated_info": new_column.model_dump()
        }

    def delete_column(self, table_name_en: str, column_name_en: str) -> Dict[str, Any]:
        """
        删除指定表中的某个字段
        Args:
            table_name_en: 表的英文名
            column_name_en: 字段的英文名
        Returns:
        """
        # 查找表
        table = next((t for t in self.tables if t.name_en == table_name_en), None)
        if not table:
            return {
                "success": False,
                "message": f'表 "{table_name_en}" 未找到',
                "updated_info": None
            }

        for idx, column in enumerate(table.columns):
            if column.name_en == column_name_en:
                del table.columns[idx]
                printf(color='green', title='删除成功', message=f'字段 "{column_name_en}" 已成功删除')
                return {
                    "success": True,
                    "message": f'字段 "{column_name_en}" 已成功删除。',
                    "updated_info": None
                }

        return {
            "success": False,
            "message": f'字段 "{column_name_en}" 未找到。',
            "updated_info": None
        }

    def edit_column(self, table_name_en: str, column_name_en: str, updates: Dict[str, Any]) -> Dict[str, Any]:
        """
        编辑指定表中的某个字段的信息
        Args:
            table_name_en:
            column_name_en:
            updates: 要更新的字段属性及其新值
        Returns:操作结果及更新后的字段信息
                {
                    "success": bool,
                    "message": str,
                    "updated_info": dict  # 更新后的字段信息
                }
        """
        if not updates:
            return {
                "success": False,
                "message": "更新内容不能为空。",
                "updated_info": None
            }

        # 查找表
        table = next((t for t in self.tables if t.name_en == table_name_en), None)
        if not table:
            return {
                "success": False,
                "message": f'表 "{table_name_en}" 未找到',
                "updated_info": None
            }

        # 查找字段
        column = next((c for c in table.columns if c.name_en == column_name_en), None)
        if not column:
            return {
                "success": False,
                "message": f'字段 "{column_name_en}" 未找到。',
                "updated_info": None
            }

        # 检查更新的字段是否合法
        allowed_fields = set(column.model_dump().keys())
        invalid_fields = [key for key in updates if key not in allowed_fields]

        if invalid_fields:
            return {
                "success": False,
                "message": f"无效的更新字段: {invalid_fields}",
                "updated_info": None
            }

        # 更新字段属性
        for key, value in updates.items():
            setattr(column, key, value)

        printf(color='green', title='更新成功', message=f'字段 "{column_name_en}" 信息已成功更新: {updates}')
        return {
            "success": True,
            "message": f'字段 "{column_name_en}" 信息已成功更新。',
            "updated_info": column.model_dump()
        }

    def add_enum(self,
                 table_name_en: str,
                 column_name_en: str,
                 enum_values: List[Any]) -> Dict[str, Any]:
        """
        向指定字段批量添加枚举值

        Args:
            table_name_en: 表的英文名。
            column_name_en: 字段的英文名。
            enum_values: 要添加的枚举值列表。

        Returns:
            Dict[str, Any]: 操作结果及更新后的枚举值列表。
        """
        # 查找表
        table = next((t for t in self.tables if t.name_en == table_name_en), None)
        if not table:
            return {
                "success": False,
                "message": f'表 "{table_name_en}" 未找到。',
                "updated_info": None
            }

        # 查找字段
        column = next((c for c in table.columns if c.name_en == column_name_en), None)
        if not column:
            return {
                "success": False,
                "message": f'字段 "{column_name_en}" 未找到。',
                "updated_info": None
            }

        # 添加新枚举值
        # 如果enum_manager有es服务：
        if self.enum_manager.enum_es:
            if self.enum_manager.enum_es.es_client.es_client:
                self.enum_manager.enum_es.add_enums_es(table_name_en, column_name_en, enum_values)

                printf(color='green', title='添加成功',
                       message=f'枚举值 {enum_values} 已成功添加到字段 "{column_name_en}"。')
                return {
                    "success": True,
                    "message": f'枚举值 {enum_values} 已成功添加到字段 "{column_name_en}"。',
                    "updated_info": enum_values
                }

        self.enum_manager.add_enum(table_name_en=table_name_en, column_name_en=column_name_en, values=enum_values)
        printf(color='green', title='添加成功', message=f'枚举值 {enum_values} 已成功添加到字段 "{column_name_en}"。')
        return {
            "success": True,
            "message": f'枚举值 {enum_values} 已成功添加到字段 "{column_name_en}"。',
            "updated_info": enum_values
        }

    def delete_enum(self,
                    table_name_en: str,
                    column_name_en: str,
                    enum_values: List[Any]) -> Dict[str, Any]:
        """
        从指定字段批量删除枚举值
        Args:
            table_name_en: 表的英文名。
            column_name_en: 字段的英文名。
            enum_values: 要删除的枚举值列表。

        Returns:
            Dict[str, Any]: 操作结果及更新后的枚举值列表。
        """
        # 查找表
        table = next((t for t in self.tables if t.name_en == table_name_en), None)
        if not table:
            return {
                "success": False,
                "message": f'表 "{table_name_en}" 未找到',
                "updated_info": None
            }

        # 查找字段
        column = next((c for c in table.columns if c.name_en == column_name_en), None)
        if not column:
            return {
                "success": False,
                "message": f'字段 "{column_name_en}" 未找到。',
                "updated_info": None
            }

        if self.enum_manager.enum_es is not None:
            if self.enum_manager.enum_es.es_client is not None:
                self.enum_manager.enum_es.delete_enums_es(table_name_en, column_name_en, enum_values)
                printf(color='green', title='删除成功',
                       message=f'枚举值 {enum_values} 已成功从字段 "{column_name_en}" 中删除。')
                return {
                    "success": True,
                    "message": f'枚举值 {enum_values} 已成功从字段 "{column_name_en}" 中删除。',
                    "updated_info": enum_values
                }

        # 删除枚举值
        try:
            self.enum_manager.delete_enum(table_name_en=table_name_en,
                                          column_name_en=column_name_en,
                                          values=enum_values)

            printf(color='green', title='删除成功',
                   message=f'枚举值 {enum_values} 已成功从字段 "{column_name_en}" 中删除。')
            return {
                "success": True,
                "message": f'枚举值 {enum_values} 已成功从字段 "{column_name_en}" 中删除。',
                "updated_info": enum_values
            }
        except Exception as e:
            return {
                "success": False,
                "message": f'枚举值 {enum_values} 删除失败, {e}',
                "updated_info": None
            }

    def __del__(self):
        """
        析构函数，当实例生命周期结束后，临时sqlite正确释放
        这里会导致后面sqlite的莫名其妙关掉，但是我加了自动创建连接的方法，暂时可以忽略，
        Returns:

        """
        try:
            if hasattr(self, 'conn'):
                self.conn.close()
            if hasattr(self, 'temp_manager'):
                self.temp_manager.cleanup()
            printf(color='green', title='资源已释放')
        except Exception as e:
            printf(color='red', title='Error during cleanup', message=str(e))

    def add_knowledge(self, content: str, metadata: Any):
        """
        直接添加知识到知识库，
        例如： content: 新增净增比的定义，metadata: {sql:'xxx', explain:xx}
        Args:
            content: 知识的标题
            metadata: 知识的内容
        Returns:
        """
        from chatbi.rag.chunk import Chunk
        chunk = Chunk(content=content, metadata=metadata)
        self.knowledge.add_extra_knowledge(knowledge_chunks=[chunk])
        pass

    def add_extra_knowledge(self, knowledge_chunks):
        return self.knowledge.add_extra_knowledge(knowledge_chunks=knowledge_chunks) if self.knowledge else None

    # ----------------------- 持久化管理 -----------------------
    def dump(self, save_dir: str) -> None:
        """
        保存当前Datasource对象为 PKL 文件，用于后续直接加载

        Args:
            save_dir (str): 保存的目录路径
        """
        import dill

        if not save_dir:
            raise ValueError("save_dir 不能为空，请提供有效的保存路径。")

        if not os.path.exists(save_dir):
            try:
                os.makedirs(save_dir)
            except Exception as e:
                raise ValueError(f"无法创建目录 {save_dir}: {e}")

        if not os.path.isdir(save_dir):
            raise ValueError(f"{save_dir} 不是一个有效的目录路径。")

        save_file_name = f"{self.source}.pkl"
        save_path = os.path.join(save_dir, save_file_name)

        try:
            with open(save_path, 'wb') as f:
                dill.dump(self, f)
            print(f"模型已成功保存到 {save_path}")
        except Exception as e:
            raise RuntimeError(f"保存文件失败：{e}")

    def get_table_description(self,
                              table_name: str,
                              llm: "BaseLLM") -> str:
        """
        获取数据表的总结性描述，用大模型
        Args:
            table_name:表英文
            llm: 大模型
        Returns: String
        """

        if table_name is None:
            raise ValueError("Table Name is None")

        table = next((t for t in self.tables if t.name_en == table_name or t.name_cn == table_name), None)

        if not table:
            raise ValueError(f"表 {table_name} 不存在。")

        if not llm:
            raise ValueError("LLM Cannot be None")

        prompt = f"{self.adaptive_schema(target_table=table_name)}\n请为以上数据表生成一段200字以内的总结性描述。"
        resp = llm.invoke(prompt)
        return resp

    def __str__(self):
        return str(self.get_ddls())


if __name__ == '__main__':
    # x = {"level": "admin", "permissions": ["read", "write"]}
    # access_info = AccessInfo(permissions=x)
    # c = ColumnInfo(name_en="test_col", access_info=access_info)
    # print(c.model_dump())
    pass
