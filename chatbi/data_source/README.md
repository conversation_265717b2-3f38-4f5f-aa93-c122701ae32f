# DataSource

通过DataSource为各种数据源（如 SQL Server、PostgreSQL、MySQL、SQLite 等）提供统一的接口和基础功能。通过继承 `DataSource` 基类，可快速实现与不同数据源的连接，并对数据表进行结构化管理和操作。

## 支持DBMS
- Sqlite
- Mysql
- Excel/CSV
- PostgreSQL
- ClickHouse
- SQL Server
- Schema Source（Schema表信息，本质上也可以用Sqlite替代）

## 功能介绍

### 1. 统一接口管理
- **统一数据源接口**：为不同类型的数据源提供统一的接口，使得 SQL Server、PostgreSQL、MySQL 等数据库的管理变得一致和简洁。
- **灵活扩展**：通过继承 `DataSource` 基类，快速扩展以支持更多数据源类型，实现个性化的数据初始化和操作方法。

### 2. 表与字段管理
- **表结构定义**：可以定义数据表的元数据，包括表名、字段名、字段类型、字段注释等。
- **支持多语言**：支持中英文表名和字段名，自动根据语言设置进行处理。
- **示例数据与枚举值**：每个字段可附加示例数据和可能的枚举值，方便数据样本的快速构建和校验。

### 3. 数据操作功能
- **动态表操作**：支持数据表的添加、合并、筛选和动态查询，简化多数据源的管理。
- **查找和过滤**：能够根据字段名或字段值查找相关表，并返回匹配的详细字段信息。

### 4. 数据质量与完整性检查
- **数据完整性验证**：对表和字段的定义进行完整性和一致性检查，确保数据源的可靠性。
- **异常检测**：提供数据表的空值、重复值等基础质量检测功能，帮助发现潜在问题。

### 5. 数据导出与展示
- **导出为 JSON**：支持将表结构和元数据导出为 JSON 文件，便于与其他系统集成。
- **信息展示**：可以详细展示表和字段的信息，方便开发调试。

## 使用方法

### 1. 继承 `DataSource` 基类

通过继承 `DataSource` 基类，并重载`initialize_data`与`execute`方法，实现对特定数据源的连接和数据初始化。例如，实现一个与 SQL Server 连接的数据源类：

```python
from chatbi.data_source.base import DataSource, TableInfo, ColumnInfo
import pyodbc  # SQL Server 驱动示例

class SQLServerDataSource(DataSource):
    def __init__(self, connection_string: str, language='cn'):
        self.connection_string = connection_string
        self.connection = pyodbc.connect(self.connection_string)
        super().__init__(language=language)

    def initialize_data(self):
        # 示例：从 SQL Server 中获取表和字段信息
        cursor = self.connection.cursor()
        cursor.execute("SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE='BASE TABLE'")
        tables = cursor.fetchall()

        for table in tables:
            table_name = table.TABLE_NAME
            cursor.execute(f"SELECT COLUMN_NAME, DATA_TYPE FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='{table_name}'")
            columns = [ColumnInfo(name_en=col.COLUMN_NAME, col_type=col.DATA_TYPE) for col in cursor.fetchall()]

            self.tables.append(TableInfo(name_en=table_name, columns=columns))
    
    def execute(self, sql: str) -> Optional[pd.DataFrame]:
        # 实现 SQL 执行逻辑
        return pd.read_sql(sql, self.connection)

# 初始化 SQL Server 数据源
ds = SQLServerDataSource(connection_string="Driver={SQL Server};Server=your_server;Database=your_db;Trusted_Connection=yes;")
```

## 主要功能和使用示例

`DataSource` 类中每个功能的使用方法和简单描述：

### 功能和使用方法

- **查看数据源中的表英文名：`get_table_names_en()`**
    - **描述**：获取当前数据源中所有表的英文名称。
    - **使用方法**：调用 `data_source.get_table_names_en()` 返回所有表的英文名列表。

- **查看数据表中的表中文名：`get_table_names_cn()`**
    - **描述**：获取当前数据源中所有表的中文名称。
    - **使用方法**：调用 `data_source.get_table_names_cn()`，返回所有表的中文名列表（仅在语言设置为中文时有效）。

- **查看指定表的完整信息：`display_table_info(table_name: str)`**
    - **描述**：打印指定表的完整信息，包括表名、字段信息和 DDL。
    - **使用方法**：调用 `data_source.display_table_info('table_name')` 打印指定表的详细信息。

- **获取数据表的建表语句（DDL）：`get_ddls(table_name=None)`**
    - **描述**：获取指定表的建表语句（DDL）。如果未指定表名，则返回所有表的 DDL。
    - **使用方法**：`data_source.get_ddls('table_name')` 获取单个表的 DDL 或 `data_source.get_ddls()` 获取所有表的 DDL。

- **查找包含特定字段的表：`find_tables_by_column(column_name: str)`**
    - **描述**：查找包含特定字段（英文名）的表。
    - **使用方法**：调用 `data_source.find_tables_by_column('column_name')` 返回包含该字段的表名列表。

- **查找包含指定值的表及字段信息：`find_tables_by_value(value: Any)`**
    - **描述**：根据字段的示例数据或枚举值查找包含特定值的表，并返回包含该值的表和字段信息。
    - **使用方法**：`data_source.find_tables_by_value('value')` 返回包含该值的表和字段信息的元组。

- **根据示例数据或枚举值模糊匹配查找包含特定值的表：`find_tables_by_value_fuzzy(value: Any, threshold: float = 0.7)`**
    - **描述**：模糊匹配查找包含特定值的表，返回包含该值的表名、字段信息和最匹配的值。
    - **使用方法**：`data_source.find_tables_by_value_fuzzy('value', 0.7)` 根据相似度阈值查找最接近的匹配。

- **快速版本的模糊匹配查找：`find_tables_by_value_fuzzy_fast(value: Any, threshold: float = 0.7)`**
    - **描述**：快速查找包含特定值的表，只要找到匹配的数据便立即返回。
    - **使用方法**：`data_source.find_tables_by_value_fuzzy_fast('value', 0.7)`，更高效但可能返回较少结果。

- **验证数据源中的数据完整性和一致性：`validate_data_integrity()`**
    - **描述**：检查 `self.tables` 中的数据完整性和一致性，确保表和字段的信息正确。
    - **使用方法**：调用 `data_source.validate_data_integrity()`，如果数据不一致会抛出异常。

- **获取指定表和字段的枚举值：`get_column_enums(table_name: str, column_name: str)`**
    - **描述**：获取指定表和字段的枚举值，去除其中的 `None` 值。
    - **使用方法**：调用 `data_source.get_column_enums('table_name', 'column_name')` 返回枚举值列表。

- **获取指定表的所有字段英文名：`get_column_en_names(table_name: Optional[str] = None)`**
    - **描述**：获取指定表或所有表的字段英文名列表。
    - **使用方法**：`data_source.get_column_en_names('table_name')` 获取指定表的字段英文名，或 `data_source.get_column_en_names()` 获取所有字段。

- **获取指定表的所有字段中文名：`get_column_cn_names(table_name: Optional[str] = None)`**
    - **描述**：获取指定表或所有表的字段中文名列表（仅在语言为中文时有效）。
    - **使用方法**：`data_source.get_column_cn_names('table_name')` 获取指定表的字段中文名，或 `data_source.get_column_cn_names()` 获取所有字段。

- **匹配数据源中最相似的表英文名：`match_table_name_en(table_name: str, method: str = 'jac')`**
    - **描述**：根据相似度计算方法匹配数据源中最接近的表英文名。
    - **使用方法**：`data_source.match_table_name_en('table_name')` 使用默认方法匹配，或指定方法 `'jac'`、`'lev'`、`'dice'`。

- **匹配数据源中最相似的字段名：`match_column_name_en(column_name: str, table_name: Optional[str] = None, method: str = 'jac')`**
    - **描述**：匹配指定表或所有表中最相似的字段名。
    - **使用方法**：`data_source.match_column_name_en('column_name')` 匹配字段，或限定在指定表中查找。

- **匹配字段的枚举值：`match_enums(table_name: str, column_name: str, value: str, method: str = 'jac')`**
    - **描述**：匹配字段的枚举值，找到与输入值最相似的枚举。
    - **使用方法**：`data_source.match_enums('table_name', 'column_name', 'value')` 匹配最相似的枚举值。

- **匹配给定值与字段的枚举值的最长公共子串：`match_longest_common_substring(value: str, table_name: str, column_name: str)`**
    - **描述**：匹配输入值与指定字段枚举值的最长公共子串，并返回带有 '%' 包围的匹配形式。
    - **使用方法**：`data_source.match_longest_common_substring('value', 'table_name', 'column_name')`。

- **添加新的数据源：`append(data_source: 'DataSource')`**
    - **描述**：将新的数据源追加到当前实例中，确保数据源语言一致。
    - **使用方法**：`data_source.append(another_data_source)` 将另一个数据源的内容添加到当前实例。

- **根据表名列表筛选数据源中的表：`filter_tables(table_names: List[str])`**
    - **描述**：根据指定的表名列表，创建一个新的数据源实例，仅包含所选表。
    - **使用方法**：`data_source.filter_tables(['table_name1', 'table_name2'])` 返回仅包含指定表的新数据源。

- **导出数据源的表信息为 JSON 文件：`export_to_json(filepath: str)`**
    - **描述**：将数据源的表信息导出为 JSON 格式的文件。
    - **使用方法**：`data_source.export_to_json('path/to/file.json')` 导出表信息到指定路径。

- **初始化数据源：`initialize_data()`（需要子类实现）**
    - **描述**：抽象方法，用于初始化数据源并将其转换为 `TableInfo`，子类必须实现。
    - **使用方法**：由子类实现并调用，无直接使用。

- **执行 SQL 代码：`execute(**kwargs)`（需要子类实现）**
    - **描述**：抽象方法，用于执行 SQL 代码并返回结果，子类必须实现。
    - **使用方法**：由子类实现具体的 SQL 执行逻辑。

这些描述提供了每个功能的用途和如何使用它们的简单指导，方便理解和操作 `DataSource` 类的各种功能。