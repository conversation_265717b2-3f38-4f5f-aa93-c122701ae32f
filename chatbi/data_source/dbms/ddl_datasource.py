"""
用于根据MySQL的DDL语句创建数据源。
使用sqlglot解析DDL语句，支持中文注释。

使用示例:
CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT COMMENT '用户ID',
    username VARCHAR(50) NOT NULL COMMENT '用户名',
    email VARCHAR(100) COMMENT '电子邮箱'
) COMMENT='用户信息表';
"""
from typing import List, Dict, Any
import sqlite3
import os
import sqlglot
from sqlglot.expressions import Create, Column, Table, Comment
from chatbi.data_source.dbms.sqlite_datasource import SqliteDataSource
from chatbi.data_source.base import TableInfo, ColumnInfo
from chatbi.utils.temporary_file_manager import TempFileManager
from chatbi.utils.printf import printf


class DDLDataSource(SqliteDataSource):
    type = 'schema'
    database = 'mysql'

    def __init__(self,
                 source: str,
                 ddl_statements: str,
                 language: str = 'cn',
                 embedding_model=None,
                 es_client=None,
                 source_description: str = None):
        """
        初始化DDLDataSource
        Args:
            source: 数据源名称
            ddl_statements: MySQL DDL语句字符串
            language: 语言，默认为中文('cn')
            embedding_model: 嵌入模型（可选）
            es_client: ES客户端（可选）
            source_description: 数据源描述（可选）
        """
        # 设置临时文件管理器
        self.temp_manager = TempFileManager()

        # 保存DDL语句
        self.ddl_statements = ddl_statements

        # 创建临时SQLite文件
        self.file_name = f"{source}.sqlite"
        self.sqlite_file = self.temp_manager.create_temp_file(self.file_name)
        self.sqlite_path = os.path.join(self.temp_manager.get_path(), self.file_name)

        # 创建SQLite数据库并转换DDL
        self._create_sqlite_database()
        self._convert_and_execute_ddl()

        # 调用父类的初始化
        super().__init__(
            language=language,
            source=source,
            sqlite_path=self.sqlite_path,
            embedding_model=embedding_model,
            es_client=es_client,
            source_description=source_description
        )

        # 增强日期检测
        self.enhance_date_detection()

    def _create_sqlite_database(self):
        """创建SQLite数据库连接"""
        try:
            self.conn = sqlite3.connect(self.sqlite_path)
            printf(color='green', title='数据库创建成功', message=f'SQLite数据库已创建: {self.sqlite_path}')
        except sqlite3.Error as e:
            printf(color='red', title='数据库创建失败', message=f'创建SQLite数据库时出错: {e}')
            raise

    def _convert_mysql_to_sqlite_type(self, mysql_type: str) -> str:
        """将MySQL数据类型转换为SQLite数据类型"""
        mysql_type = mysql_type.upper().split('(')[0]  # 去掉长度信息

        type_mapping = {
            'TINYINT': 'INTEGER', 'SMALLINT': 'INTEGER', 'MEDIUMINT': 'INTEGER',
            'INT': 'INTEGER', 'INTEGER': 'INTEGER', 'BIGINT': 'INTEGER',
            'FLOAT': 'REAL', 'DOUBLE': 'REAL', 'DECIMAL': 'REAL',
            'CHAR': 'TEXT', 'VARCHAR': 'TEXT', 'TEXT': 'TEXT',
            'MEDIUMTEXT': 'TEXT', 'LONGTEXT': 'TEXT', 'DATE': 'TEXT',
            'DATETIME': 'TEXT', 'TIMESTAMP': 'TEXT', 'TIME': 'TEXT',
            'YEAR': 'INTEGER', 'BLOB': 'BLOB', 'ENUM': 'TEXT', 'SET': 'TEXT'
        }

        return type_mapping.get(mysql_type, 'TEXT')

    def _parse_table(self, create_stmt: Create) -> TableInfo:
        """使用sqlglot解析CREATE TABLE语句"""
        table_name = create_stmt.this.name  # 获取表名
        table_comment = None

        # 获取表注释
        for prop in create_stmt.properties:
            if isinstance(prop, Comment):
                table_comment = prop.this.strip("'")
                break

        # 解析列定义
        columns = []
        for col in create_stmt.columns:
            col_name = col.this.name
            col_type = str(col.args['kind']).upper()
            col_comment = None

            # 获取列注释
            for prop in col.props:
                if isinstance(prop, Comment):
                    col_comment = prop.this.strip("'")
                    break

            # 创建ColumnInfo对象
            columns.append(ColumnInfo(
                name_en=col_name,
                name_cn=col_comment,
                col_type=self._convert_mysql_to_sqlite_type(col_type),
                col_comment=col_comment,
                example_data=[],
                enums=None
            ))

        # 创建TableInfo对象
        table_info = TableInfo(
            name_en=table_name,
            name_cn=table_comment,
            columns=columns,
            description=table_comment,
            ddl=str(create_stmt)
        )

        return table_info

    def _convert_and_execute_ddl(self):
        """转换并执行DDL语句"""
        self.tables = []

        # 解析所有CREATE TABLE语句
        parsed_statements = sqlglot.parse(self.ddl_statements, read='mysql')

        for stmt in parsed_statements:
            if isinstance(stmt, Create) and isinstance(stmt.this, Table):
                table_info = self._parse_table(stmt)
                if table_info:
                    # 生成SQLite的建表语句
                    sqlite_ddl = self._generate_sqlite_ddl(table_info)

                    try:
                        # 执行SQLite建表语句
                        self.conn.execute(sqlite_ddl)
                        self.tables.append(table_info)
                        printf(color='green', title='建表成功',
                               message=f'表 {table_info.name_en} ({table_info.name_cn}) 创建成功')
                    except sqlite3.Error as e:
                        printf(color='red', title='建表失败',
                               message=f'创建表 {table_info.name_en} 时出错: {e}')
                        raise

        self.conn.commit()

    def _generate_sqlite_ddl(self, table_info: TableInfo) -> str:
        """生成SQLite的DDL语句"""
        columns = []
        for column in table_info.columns:
            col_def = f"{column.name_en} {column.col_type}"
            if column.name_cn:
                col_def += f" -- {{'字段中文': '{column.name_cn}'}}"
            columns.append(col_def)

        ddl = f"CREATE TABLE {table_info.name_en} (\n    "
        ddl += ",\n    ".join(columns)
        ddl += "\n)"

        if table_info.name_cn:
            ddl += f"\n-- {{'表中文名': '{table_info.name_cn}'}}"

        ddl += ";"
        return ddl


if __name__ == '__main__':
    # 测试用例
    ddl = """
    CREATE TABLE users (
        id INT PRIMARY KEY AUTO_INCREMENT COMMENT '用户ID',
        username VARCHAR(50) NOT NULL COMMENT '用户名',
        email VARCHAR(100) COMMENT '电子邮箱',
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
    ) COMMENT='用户信息表';
    """

    ds = DDLDataSource(source='test_ddl', ddl_statements=ddl)
    print("中文表名:", ds.get_table_names_cn())
    print("英文表名:", ds.get_table_names_en())
    print("列名:", ds.get_column_cn_names())