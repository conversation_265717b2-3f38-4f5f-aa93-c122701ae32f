import mysql.connector
from mysql.connector import Error
from chatbi.data_source.base import DataSource, TableInfo, ColumnInfo
import pandas as pd
import re
from typing import List, Dict, Optional, Any, Tuple
from chatbi.utils.printf import printf
from tqdm import tqdm
from chatbi.es.es_client import ElasticsearchClient
import asyncio


class MysqlDataSource(DataSource):
    type = 'no-schema'
    database = 'mysql'

    def __init__(self,
                 source: str,
                 host: str,
                 user: str,
                 password: str,
                 port: str,
                 database: str,
                 es_client=None,
                 embedding_model=None,
                 language: str = 'cn'):

        self.source = source
        self.host = host
        self.user = user
        self.password = password
        self.database_ = database
        self.port = port
        self.conn = self.connect_to_mysql()

        try:
            super().__init__(language=language,
                             es_client=es_client,
                             embedding_model=embedding_model,
                             source=source)
        except Exception as e:
            raise ConnectionError(f'Mysql数据库连接失败，{e}')

        self.enhance_date_detection()

    def connect_to_mysql(self):
        """连接到MySQL数据库"""
        print('正在连接数据库')
        try:
            connection = mysql.connector.connect(
                host=self.host,
                user=self.user,
                password=self.password,
                database=self.database_,
                port=self.port
            )
            if connection.is_connected():
                printf(color='green', title='MySQL数据库已成功连接!',
                       message=f'[Host]:{self.host}\n[Database]:{self.database_}')
            return connection
        except Error as e:
            printf(color='red', title='连接到MySQL失败', message=f'{e}')
            raise ValueError(f'{e}')

    async def async_connect_to_mysql(self):
        """异步连接到MySQL数据库"""
        print('正在连接数据库')
        try:
            connection = await asyncio.to_thread(lambda: mysql.connector.connect(
                host=self.host,
                user=self.user,
                password=self.password,
                database=self.database_,
                port=self.port
            ))
            if connection.is_connected():
                printf(color='green', title='MySQL数据库已成功连接!',
                       message=f'[Host]:{self.host}\n[Database]:{self.database_}')
            return connection
        except Error as e:
            printf(color='red', title='连接到MySQL失败', message=f'{e}')
            raise ValueError(f'{e}')

    def ensure_connection(self):
        """确保MySQL连接有效，如果断开则重新连接。"""
        try:
            if not self.conn.is_connected():
                printf(color='yellow', title='MySQL连接已断开', message='尝试重新连接...')
                self.conn = self.connect_to_mysql()
        except Error:
            printf(color='yellow', title='MySQL连接检查失败', message='尝试重新连接...')
            self.conn = self.connect_to_mysql()

    def get_all_mysql_table_names(self) -> List[str]:
        """获取所有表的名称。"""
        self.ensure_connection()  # 确保连接有效
        query = "SHOW TABLES;"
        cursor = self.conn.cursor()
        cursor.execute(query)
        tables = [row[0] for row in cursor.fetchall()]
        cursor.close()
        return tables

    def get_table_ddl(self, table_name: str) -> str:
        """获取指定表的DDL语句。"""
        self.ensure_connection()  # 确保连接有效
        query = f"SHOW CREATE TABLE `{table_name}`;"
        cursor = self.conn.cursor()
        try:
            cursor.execute(query)
            ddl = cursor.fetchone()[1]  # DDL 通常在返回结果的第二列
        except Error as e:
            print(f"获取表 {table_name} 的DDL时出错: {e}")
            ddl = ""
        finally:
            cursor.close()
        return ddl

    def get_column_info(self, table_name: str) -> List[ColumnInfo]:
        """获取表的列信息，并解析注释。"""
        self.ensure_connection()  # 确保连接有效
        query = f"SHOW FULL COLUMNS FROM `{table_name}`;"
        cursor = self.conn.cursor()
        cursor.execute(query)
        rows = cursor.fetchall()
        cursor.close()

        sample_data = self.get_sample_data(table_name)
        columns = []

        for row in tqdm(rows, desc=f"Processing columns for {table_name}"):
            column_name = row[0]
            column_type = row[1]
            col_comment_text = row[8]  # 列注释

            # 初始化中文名和注释
            name_cn = None
            col_comment = None
            if self.language == 'cn':
                # 假设注释格式为 '字段中文名: 注释内容'
                if ':' in col_comment_text:
                    name_cn, col_comment = map(str.strip, col_comment_text.split(':', 1))
                else:
                    # 如果没有':'，则将整个注释作为字段中文名
                    name_cn = col_comment_text
            else:
                col_comment = col_comment_text  # 非中文环境下，将注释作为字段注释

            # 获取该列的示例数据
            example_data = sample_data.get(column_name, [])

            # 获取该列的枚举值
            enums = self.get_column_enums(table_name, column_name)

            # 将枚举值存放进去
            self.enum_manager.add_enum(table_name_en=table_name,
                                       column_name_en=column_name,
                                       values=enums)

            columns.append(ColumnInfo(
                name_en=column_name,
                name_cn=name_cn,
                col_type=column_type,
                col_comment=col_comment,
                example_data=example_data,
                enums=enums
            ))

        return columns

    @staticmethod
    def extract_table_cn_name_from_ddl(ddl: str) -> Optional[str]:
        """从DDL中提取表的中文名。"""
        # 使用正则表达式提取表的 COMMENT='...'
        match = re.search(r"COMMENT='([^']+)'", ddl)
        if match:
            return match.group(1)
        return None

    def get_sample_data(self, table_name: str, limit: int = 5) -> Dict[str, List[Any]]:
        """获取表中每列的示例数据。"""
        self.ensure_connection()  # 确保连接有效
        query = f"SELECT * FROM `{table_name}` LIMIT {limit};"
        cursor = self.conn.cursor()

        try:
            cursor.execute(query)
            rows = cursor.fetchall()
            col_names = [description[0] for description in cursor.description]
        except Error as e:
            print(f"获取表 {table_name} 的示例数据时出错: {e}")
            return {}
        finally:
            cursor.close()

        sample_data = {col: [] for col in col_names}
        for row in rows:
            for col, value in zip(col_names, row):
                sample_data[col].append(value)

        return sample_data

    def get_column_enums(self, table_name: str, column_name: str, enum_limit: int = 200) -> Optional[List[Any]]:
        """获取列的枚举值。"""
        self.ensure_connection()  # 确保连接有效
        query = f'SELECT DISTINCT `{column_name}` FROM `{table_name}` LIMIT {enum_limit + 1};'
        cursor = self.conn.cursor()
        try:
            cursor.execute(query)
            enums = [row[0] for row in cursor.fetchall()]
        except Error as e:
            print(f"获取表 {table_name} 中字段 {column_name} 的枚举值时出错: {e}")
            enums = None
        finally:
            cursor.close()

        if enums and len(enums) > enum_limit:
            return None
        return enums

    def initialize_data(self):
        """将MySQL数据库中的数据转换为TableInfo和ColumnInfo的形式。"""
        table_names = self.get_all_mysql_table_names()  # 获取所有表名

        for table_name in table_names:
            ddl = self.get_table_ddl(table_name)  # 获取DDL语句

            # 如果是中文环境，尝试提取中文表名
            table_cn_name = None
            if self.language == 'cn':
                table_cn_name = self.extract_table_cn_name_from_ddl(ddl)

            columns = self.get_column_info(table_name)  # 获取列信息

            # 创建TableInfo对象
            table_info = TableInfo(
                name_en=table_name,
                name_cn=table_cn_name if self.language == 'cn' else None,
                columns=columns,
                ddl=ddl
            )

            # 将TableInfo对象添加到tables列表中
            self.tables.append(table_info)

        self._transfer_tables_into_dfs_infos()  # 将表信息和数据转换为DataFrame并存储到infos和dfs中

    async def async_execute(self, sql: str) -> Optional[pd.DataFrame]:
        """
        异步执行SQL查询并返回结果
        Args:
            sql: str SQL语句
        Returns:
            Tuple[Optional[pd.DataFrame], Optional[str]]: (结果数据框, 错误信息)
        """
        self.ensure_connection()  # 同步的连接检查
        try:
            res = await asyncio.to_thread(lambda: pd.read_sql(sql, self.conn))
            return res
        except Error as error:
            print(f"执行SQL时出错: {error}")
            raise error

    def execute(self, sql: str = None) -> Optional[pd.DataFrame]:

        self.ensure_connection()  # 同步的连接检查
        try:
            res = pd.read_sql(sql, self.conn)
            return res
        except Error as error:
            print(f"执行SQL时出错: {error}")
            raise error

    def export_table_to_csv(self, table_name: str, file_path: str) -> None:
        """导出指定表的数据到CSV文件。"""
        self.ensure_connection()  # 确保连接有效
        query = f"SELECT * FROM `{table_name}`;"
        try:
            df = pd.read_sql(query, self.conn)
            df.to_csv(file_path, index=False)
            print(f"表 {table_name} 已导出到 {file_path}")
        except Error as e:
            print(f"导出表 {table_name} 到CSV时出错: {e}")

    def close_connection(self):
        """关闭MySQL连接。"""
        if self.conn.is_connected():
            self.conn.close()
            print("MySQL连接已关闭。")

    def __getstate__(self):
        """
        在序列化时移除 `self.conn` 属性，并保存其他状态
        """
        state = self.__dict__.copy()
        state['conn'] = None  # 不序列化 MySQL 连接对象
        # 保留本地枚举值
        state['local_enums'] = self.enum_manager.local_store

        # 如果有ES，则删除与ES客户端连接相关的属性
        if state['es_client'] is not None:
            state['es_client'] = None
            state['enum_manager'] = None
            state['knowledge'] = None

        return state

    def __setstate__(self, state):
        """
        在反序列化时重新创建 `self.conn` 属性
        """
        self.__dict__.update(state)
        # 重新建立数据库连接
        self.conn = self.connect_to_mysql()
        # 在反序列化的时候重新建立ES连接
        if self.es_config != {}:
            try:
                self.es_client = ElasticsearchClient(**self.es_config)
            except Exception as e:
                print(e)
            self._initialize_knowledge(self.es_client, self.embedding_model)
            self.enum_manager.local_store = self.local_enums


