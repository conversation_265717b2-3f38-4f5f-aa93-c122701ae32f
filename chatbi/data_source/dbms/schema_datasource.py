"""
用于适配schema文件：
Excel里面包含了两个sheet，sheet1是示例文件、sheet2是表结构信息；

20240905 Tian
"""
from chatbi.utils.printf import printf
import pandas as pd
import os
import sqlite3
from typing import List, Dict
from chatbi.data_source.dbms.sqlite_datasource import SqliteDataSource
from chatbi.utils.temporary_file_manager import TempFileManager


class SchemaDataSource(SqliteDataSource):
    type = 'schema'
    database = 'sqlite'

    def __init__(self,
                 source: str,
                 schema_path: str,
                 sqlite_path: str = None,
                 language: str = 'cn',
                 embedding_model=None,
                 es_client=None, ):
        """
        初始化SchemaDataSource
        Args:
            schema_path: Excel文件所在目录的路径
            language: 数据语言，默认为中文('cn')
        """
        # 创建一个临时的Sqlite文件
        if sqlite_path is None:
            self.temp_manager = TempFileManager()  # 临时文件管理器
            self.file_name = f"{source}.sqlite"
            self.sqlite_file = self.temp_manager.create_temp_file(self.file_name)
            self.sqlite_path = os.path.join(self.temp_manager.get_path(), self.file_name)
        else:
            self.sqlite_path = sqlite_path

        self.directory_path = schema_path
        # 检查并创建新的 SQLite 文件连接
        self._create_sqlite_database()
        self.initialize_data_from_excel()

        super().__init__(language=language,
                         source=source,
                         embedding_model=embedding_model,
                         es_client=es_client,
                         sqlite_path=self.sqlite_path)

        self.enhance_date_detection()

    def _get_data_files(self) -> List[str]:
        """
        获取指定目录下的所有 Excel 和 CSV 文件路径
        Returns: 文件路径列表。
        """
        data_files = []

        if self.directory_path:
            # 如果是单个文件路径，直接判断并添加
            if not os.path.exists(self.directory_path):
                raise FileNotFoundError(f"路径 {self.directory_path} 不存在.")

            if os.path.isfile(self.directory_path):
                if self.directory_path.endswith('.xlsx') or self.directory_path.endswith('.xls'):
                    data_files.append(self.directory_path)

            # 如果是目录，列出其中的所有文件
            elif os.path.isdir(self.directory_path):
                for file in os.listdir(self.directory_path):
                    if file.endswith('.xlsx') or file.endswith('.xls'):
                        data_files.append(os.path.join(self.directory_path, file))
        return data_files

    def _create_sqlite_database(self):
        """
        创建一个新的 SQLite 数据库文件，如果文件已经存在则清空它。
        """
        # 检查文件是否存在，如果存在则删除文件
        if os.path.exists(self.sqlite_path):
            try:
                os.remove(self.sqlite_path)
                print(f"现有的sqlite文件已被移除： {self.sqlite_path} ")
            except OSError as e:
                print(f"删除sqlite文件时发生错误: {e}")

        # 尝试连接 SQLite 数据库，如果文件不存在会自动创建
        try:
            self.conn = sqlite3.connect(self.sqlite_path)
            print(f"SQLite数据库已成功创建并连接 {self.sqlite_path}")
        except sqlite3.Error as e:
            print(f"创建Sqlite数据库时发生错误: {e}")

    @staticmethod
    def normalize_column_type(col_type: str) -> str:
        """
        将其他数据库的数据类型转换为SQLite支持的类型。
        Args:
            col_type: 原始字段类型。
        Returns: 兼容SQLite的数据类型。
        """
        col_type = col_type.upper()
        if 'INT' in col_type:
            return 'INTEGER'
        elif 'CHAR' in col_type or 'CLOB' in col_type or 'TEXT' in col_type or 'VARCHAR' in col_type:
            return 'TEXT'
        elif 'BLOB' in col_type:
            return 'BLOB'
        elif 'REAL' in col_type or 'FLOA' in col_type or 'DOUB' in col_type:
            return 'REAL'
        else:
            return 'TEXT'  # 默认转换为TEXT类型

    @staticmethod
    def _read_excel_data(file_path: str) -> Dict[str, pd.DataFrame]:
        """
        读取Excel文件中的Sheet1和Sheet2。
        Args:
            file_path: Excel文件的路径。
        Returns: 包含两个DataFrame的字典，键为'sample'和'info'
        """
        try:
            sample_data = pd.read_excel(file_path, sheet_name=0)  # 读取Sheet1
            info_data = pd.read_excel(file_path, sheet_name=1)  # 读取Sheet2
            return {'sample': sample_data, 'info': info_data}
        except Exception as e:
            print(f"Error reading Excel file {file_path}: {e}")
            return {}

    def _generate_create_table_sql(self, table_name: str, info_df: pd.DataFrame) -> (str, str):
        """
        生成SQLite的建表语句，并将中文表名、字段名和注释作为注释保存。
        Args:
            table_name: 表英文名。
            info_df: 包含字段信息的DataFrame。
        Returns: 生成的建表语句。
        """
        columns = []
        table_cn_name = info_df.get('表中文名', [None])[0] if '表中文名' in info_df else None
        table_en_name = info_df.get('表英文名', [table_name])[0] if '表英文名' in info_df else table_name
        table_en_name = table_en_name.upper()

        for _, row in info_df.iterrows():
            column_name_en = row.get('字段英文名').upper()
            column_type = row.get('字段类型', 'VARCHAR')
            # 检查字段类型是否符合sqlite标准
            column_type = self.normalize_column_type(column_type)
            column_name_cn = row.get('字段中文名', '')
            col_comment = row.get('字段注释', '')

            # 构建字段定义，包含注释
            column_def = f'{column_name_en} {column_type}, -- {{"字段中文": "{column_name_cn}", "注释": "{col_comment}"}}'
            columns.append(column_def)

        # 把最后一行去掉第一个逗号，要不然SQLITE会报语法错误。
        columns.pop()
        columns.append(f'{column_name_en} {column_type} -- {{"字段中文": "{column_name_cn}", "注释": "{col_comment}"}}')

        # 拼接建表语句
        create_table_sql = f'CREATE TABLE {table_en_name} ('

        # 如果有表中文名，将注释添加在建表语句的下一行
        if table_cn_name:
            create_table_sql += f'\n    -- {{"表中文名": "{table_cn_name}"}}'

        # 拼接字段定义部分
        columns_str = "\n    ".join(columns)
        create_table_sql += f'\n    {columns_str}\n);'

        return create_table_sql, table_en_name

    def _create_table_and_insert_data(self, table_name: str, sample_df: pd.DataFrame, info_df: pd.DataFrame):
        """
        创建表并插入数据到SQLite。
        Args:
            table_name: 表名。
            sample_df: 样本数据。
            info_df: 字段信息。
        """
        create_table_sql, table_name = self._generate_create_table_sql(table_name, info_df)

        # 检查数据类型并找到超大整数，同时将该列转换为字符串
        for column in sample_df.columns:
            def process_value(x):
                if isinstance(x, int) or isinstance(x, float) and x > 2 ** 45 - 1:
                    return str(x)
                return x

            sample_df[column] = sample_df[column].apply(process_value)
            # 将整个列强制转换为字符串，防止类型冲突
            sample_df[column] = sample_df[column].astype(str)

        try:
            self.conn.execute(create_table_sql)  # 执行建表语句
            print(f"数据表: {table_name} 成功创建.")
            # 插入数据
            sample_df.to_sql(table_name, self.conn, index=False, if_exists='append')
            print(f"数据已成功插入至: {table_name}")
        except Exception as e:
            printf(color='red', title=f"创建数据表或插入数据时错误.", message=f'错误: {e}')

    def initialize_data_from_excel(self) -> None:
        """
        从Excel文件中初始化数据，将每个Excel文件转换为SQLite中的表
        """
        excel_files = self._get_data_files()

        for file_path in excel_files:
            # Validate
            try:
                self.validate_schema_file(file_path=file_path)
            except Exception as e:
                raise e
            excel_data = self._read_excel_data(file_path)
            sample_df = excel_data.get('sample', pd.DataFrame())
            info_df = excel_data.get('info', pd.DataFrame())

            if info_df.empty or sample_df.empty:
                print(f"Skipping file {file_path} due to missing data in sheets.")
                continue

            # 假设文件名为表英文名
            table_name_en = os.path.splitext(os.path.basename(file_path))[0]

            # 创建表并插入数据
            self._create_table_and_insert_data(table_name_en, sample_df, info_df)

    @staticmethod
    def validate_schema_file(file_path: str) -> None:
        """
        校验用户上传的 Schema 文件是否符合要求
        Args:
            file_path (str): schema 的路径
        Raises：
            ValueError: 如果文件不符合要求，则抛出错误
        """
        if not file_path.lower().endswith(('.xlsx', '.xls')):
            raise ValueError("请上传有效的 Excel 文件")

        try:
            xls = pd.ExcelFile(file_path)
            sheets = xls.sheet_names

            # 2. 校验 Sheet 数量
            if len(sheets) != 2:
                raise ValueError("Schema 文件必须包含两个 Sheet（sheet1: 数据样例，sheet2: 数据描述）")

            # 读取 Sheet 数据
            first_sheet = pd.read_excel(xls, sheet_name=sheets[0], dtype=str)
            second_sheet = pd.read_excel(xls, sheet_name=sheets[1], dtype=str)

            # 3. 校验第二个 Sheet 是否包含必要字段
            required_columns = {'表英文名', '表中文名', '字段英文名', '字段中文名', '字段类型', '字段注释'}
            missing_columns = required_columns - set(second_sheet.columns)
            if missing_columns:
                raise ValueError(f"数据描述中缺少以下字段: {', '.join(missing_columns)}")

            # 4. 校验表英文名和表中文名是否唯一
            for col in ['表英文名', '表中文名']:
                if second_sheet[col].nunique() != 1:
                    raise ValueError(f"数据描述中的 {col} 列的值必须全部相同")

            # 5. 校验字段英文名与数据样例的列名是否一致
            first_sheet_columns = set(first_sheet.columns)
            second_sheet_field_names = set(second_sheet['字段英文名'])

            if first_sheet_columns != second_sheet_field_names:
                missing_in_first = second_sheet_field_names - first_sheet_columns
                missing_in_second = first_sheet_columns - second_sheet_field_names
                error_message = "数据描述中的字段英文名必须与数据示例中的列名一致"
                if missing_in_first:
                    error_message += f"，缺少字段: {', '.join(missing_in_first)}"
                if missing_in_second:
                    error_message += f"，额外字段: {', '.join(missing_in_second)}"
                raise ValueError(error_message)

            print("文件校验通过")

        except Exception as e:
            raise ValueError(f"Schema 校验失败: {e}")


if __name__ == '__main__':
    path = '/Users/<USER>/Code/chatbi_schemas/IHR/DTS_EMPL_FULL_DTL_MTHLY.xlsx'
    sd = SchemaDataSource(source='x', schema_path=path)
