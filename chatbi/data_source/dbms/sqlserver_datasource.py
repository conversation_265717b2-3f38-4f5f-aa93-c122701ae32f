import pyodbc
from chatbi.data_source.base import DataSource, TableInfo, ColumnInfo
import pandas as pd
import re
from typing import List, Dict, Optional, Any, Tuple


class SqlServerDataSource(DataSource):
    type = 'no-schema'
    database = 'sqlserver'

    def __init__(self, server: str, database: str, user: str, password: str, language: str = 'cn'):
        self.server = server
        self.database = database
        self.user = user
        self.password = password
        self.conn = self.connect_to_sql_server()
        super().__init__(language=language)

    def connect_to_sql_server(self):
        """连接到SQL Server数据库。"""
        try:
            connection = pyodbc.connect(
                f"DRIVER={{ODBC Driver 17 for SQL Server}};"
                f"SERVER={self.server};"
                f"DATABASE={self.database};"
                f"UID={self.user};"
                f"PWD={self.password};"
            )
            print("已成功连接到SQL Server数据库。")
            return connection
        except pyodbc.Error as e:
            print(f"连接到SQL Server时出错: {e}")
            return None

    def get_all_sqlserver_table_names(self) -> List[str]:
        """获取数据库中所有表的名称。"""
        query = "SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE';"
        cursor = self.conn.cursor()
        cursor.execute(query)
        tables = [row[0] for row in cursor.fetchall()]
        cursor.close()
        return tables

    def get_table_ddl(self, table_name: str) -> str:
        """获取指定表的DDL语句。"""
        # SQL Server不直接支持获取DDL语句，这里手动拼接DDL作为替代方案
        query = f"""
        SELECT 
            COLUMN_NAME, 
            DATA_TYPE, 
            CHARACTER_MAXIMUM_LENGTH, 
            COLUMN_DEFAULT, 
            IS_NULLABLE 
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_NAME = '{table_name}';
        """
        cursor = self.conn.cursor()
        cursor.execute(query)
        ddl = f"CREATE TABLE {table_name} (\n"
        for row in cursor.fetchall():
            col_def = f"    {row[0]} {row[1]}"
            if row[2]:
                col_def += f"({row[2]})"
            if row[4] == 'NO':
                col_def += " NOT NULL"
            if row[3]:
                col_def += f" DEFAULT {row[3]}"
            ddl += col_def + ",\n"
        ddl = ddl.rstrip(',\n') + "\n);"
        cursor.close()
        return ddl

    def get_column_info(self, table_name: str) -> List[ColumnInfo]:
        """获取表的列信息，并解析注释。"""
        query = f"""
        SELECT 
            COLUMN_NAME, 
            DATA_TYPE, 
            COLUMN_DEFAULT, 
            IS_NULLABLE 
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_NAME = '{table_name}';
        """
        cursor = self.conn.cursor()
        cursor.execute(query)
        columns = []
        sample_data = self.get_sample_data(table_name)
        for row in cursor.fetchall():
            column_name = row[0]
            column_type = row[1]
            col_comment_text = None  # SQL Server 不支持直接获取列的注释

            # 初始化中文名和注释
            name_cn = None
            if self.language == 'cn':
                col_comment = self.extract_comment_from_ddl(table_name, column_name)
                name_cn = col_comment.get('字段中文', None)
                col_comment_text = col_comment.get('注释', col_comment_text)

            # 获取该列的示例数据
            example_data = sample_data.get(column_name, [])

            # 获取该列的枚举值
            enums = self.get_column_enums(table_name, column_name)

            columns.append(ColumnInfo(
                name_en=column_name,
                name_cn=name_cn,
                col_type=column_type,
                col_comment=col_comment_text,
                example_data=example_data,
                enums=enums
            ))
        cursor.close()
        return columns

    def extract_comment_from_ddl(self, table_name: str, column_name: str) -> Dict[str, str]:
        """从DDL中提取中文名和注释。"""
        ddl = self.get_table_ddl(table_name)
        pattern = re.compile(rf"{column_name}.*?--\s*\{{(.*?)\}}", re.S)
        match = pattern.search(ddl)
        if match:
            comment_json = "{" + match.group(1).strip() + "}"
            try:
                comment_data = eval(comment_json)  # 将匹配结果转换为字典
                return comment_data
            except SyntaxError:
                return {}
        return {}

    def extract_table_cn_name_from_ddl(self, ddl: str) -> Optional[str]:
        """从DDL中提取表的中文名。"""
        match = re.search(r'--\s*\{"table_chinese_name":\s*"([^"]+)"', ddl)
        if match:
            return match.group(1)
        return None

    def get_sample_data(self, table_name: str, limit: int = 5) -> Dict[str, List[Any]]:
        """获取表中每列的示例数据。"""
        query = f"SELECT TOP {limit} * FROM {table_name};"
        cursor = self.conn.cursor()
        try:
            cursor.execute(query)
            rows = cursor.fetchall()
            col_names = [description[0] for description in cursor.description]
        except pyodbc.Error as e:
            print(f"获取表 {table_name} 的示例数据时出错: {e}")
            return {}
        finally:
            cursor.close()

        sample_data = {col: [] for col in col_names}
        for row in rows:
            for col, value in zip(col_names, row):
                sample_data[col].append(value)

        return sample_data

    def get_column_enums(self, table_name: str, column_name: str, enum_limit: int = 200) -> Optional[List[Any]]:
        """获取列的枚举值。"""
        query = f'SELECT DISTINCT [{column_name}] FROM [{table_name}] ORDER BY [{column_name}] OFFSET 0 ROWS FETCH NEXT {enum_limit + 1} ROWS ONLY;'
        cursor = self.conn.cursor()
        try:
            cursor.execute(query)
            enums = [row[0] for row in cursor.fetchall()]
        except pyodbc.Error as e:
            print(f"获取表 {table_name} 中字段 {column_name} 的枚举值时出错: {e}")
            enums = None
        finally:
            cursor.close()

        if enums and len(enums) > enum_limit:
            return None
        return enums

    def initialize_data(self):
        """将SQL Server中的数据转换为TableInfo和ColumnInfo的形式。"""
        table_names = self.get_all_sqlserver_table_names()  # 获取所有表名

        for table_name in table_names:
            ddl = self.get_table_ddl(table_name)  # 获取DDL语句

            # 如果是中文环境，尝试提取中文表名
            table_cn_name = None
            if self.language == 'cn':
                table_cn_name = self.extract_table_cn_name_from_ddl(ddl)

            columns = self.get_column_info(table_name)  # 获取列信息

            # 创建TableInfo对象
            table_info = TableInfo(
                name_en=table_name,
                name_cn=table_cn_name if self.language == 'cn' else None,
                columns=columns,
                ddl=ddl
            )

            # 将TableInfo对象添加到tables列表中
            self.tables.append(table_info)

        self._transfer_tables_into_dfs_infos()  # 将表信息和数据转换为DataFrame并存储到infos和dfs中

    def execute(self, sql: str) -> Tuple[Optional[pd.DataFrame], Optional[str]]:
        """执行SQL查询并返回结果。"""
        try:
            res = pd.read_sql(sql, self.conn)
            return res, None
        except pyodbc.Error as error:
            print(f"执行SQL时出错: {error}")
            return None, str(error)

    def export_table_to_csv(self, table_name: str, file_path: str) -> None:
        """导出指定表的数据到CSV文件。"""
        query = f"SELECT * FROM [{table_name}];"
        try:
            df = pd.read_sql(query, self.conn)
            df.to_csv(file_path, index=False)
            print(f"表 {table_name} 已导出到 {file_path}")
        except pyodbc.Error as e:
            print(f"导出表 {table_name} 到CSV时出错: {e}")

    def close_connection(self):
        """关闭SQL Server连接。"""
        if self.conn:
            self.conn.close()
            print("SQL Server连接已关闭。")
