"""
用于连接Sqlite数据。
仅需传入sqlite，即可读取数据库中的各表的ddl。
对于中文的问答场景，需要SQLite数据库在建表时备注，例如以下格式

CREATE TABLE user_info (
    -- {"表中文名": "用户信息表", "注释": "存储用户基本信息"}
    id INTEGER PRIMARY KEY AUTOINCREMENT,  -- {"字段中文": "用户ID", "注释": "用户唯一标识"}
    username TEXT NOT NULL,                -- {"字段中文": "用户名", "注释": "用户的登录名"}
    password TEXT NOT NULL,                -- {"字段中文": "密码", "注释": "用户的登录密码"}
    email TEXT,                            -- {"字段中文": "电子邮件", "注释": "用户的邮箱地址"}
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP  -- {"字段中文": "创建时间", "注释": "记录创建的时间"}
);

最终执行的也是英文表名、字段名。
"""

from chatbi.data_source.base import DataSource, TableInfo, ColumnInfo
import sqlite3
import pandas as pd
from chatbi.utils.printf import printf
import re
from typing import Dict, Optional, List, Any, Tuple
from chatbi.es.es_client import ElasticsearchClient
from tqdm import tqdm

import asyncio


class SqliteDataSource(DataSource):
    type = 'no-schema'
    database = 'sqlite'

    def __init__(self,
                 source: str,
                 sqlite_path: str,
                 language: str = 'cn',
                 embedding_model=None,
                 es_client=None,
                 source_description: str = None
                 ):

        self.path = sqlite_path
        self.conn = sqlite3.connect(self.path, check_same_thread=False)  # 允许多线程连接

        super().__init__(language=language,
                         source=source,
                         embedding_model=embedding_model,
                         es_client=es_client,
                         source_description=source_description)

        self.enhance_date_detection()

    def _get_all_sqlite_table_names(self) -> List[str]:
        """获取数据库中所有表的名称"""
        query = "SELECT name FROM sqlite_master WHERE type='table';"
        cursor = self.conn.cursor()
        cursor.execute(query)
        tables = [row[0] for row in cursor.fetchall()]
        cursor.close()
        return tables

    def _get_table_ddl(self, table_name: str) -> str:
        """通过sqlite获取到输入数据表名对应的ddl语句"""
        query = f"SELECT sql FROM sqlite_master WHERE type='table' AND name='{table_name}';"
        # 手动管理游标的打开和关闭
        cursor = self.conn.cursor()
        try:
            cursor.execute(query)
            ddl = cursor.fetchone()[0]
        except sqlite3.Error as e:
            print(f"Error fetching DDL for table {table_name}: {e}")
            ddl = ""
        return ddl

    def _get_column_info(self, table_name: str) -> List[ColumnInfo]:
        """获取表的列信息，并解析注释"""
        query = f"PRAGMA table_info({table_name});"
        cursor = self.conn.cursor()
        cursor.execute(query)
        columns = []
        sample_data = self._get_sample_data(table_name)
        for row in tqdm(cursor.fetchall(), desc=f"正在处理列信息，并加载至Enumeration中。{table_name}"):
            column_name = row[1]
            column_type = row[2]

            # 初始化中文名和注释为空
            name_cn = None
            col_comment_text = None

            # 仅在 language 为 'cn' 且存在中文注释时，提取中文信息
            if self.language == 'cn':
                col_comment = self.extract_comment_from_ddl(table_name, column_name)
                name_cn = col_comment.get('字段中文', None)
                col_comment_text = col_comment.get('注释', None)

            # 获取该列的示例数据
            example_data = sample_data.get(column_name, [])

            # 获取该列的枚举值
            enums = self._get_column_enums(table_name, column_name, column_type)

            # 将枚举值存放进去
            self.enum_manager.add_enum(table_name_en=table_name,
                                       column_name_en=column_name,
                                       values=enums)

            columns.append(ColumnInfo(
                name_en=column_name,
                name_cn=name_cn,
                col_type=column_type,
                col_comment=col_comment_text,
                example_data=example_data,
                enums=enums
            ))

        cursor.close()
        return columns

    def extract_comment_from_ddl(self, table_name: str, column_name: str) -> Dict[str, str]:
        """从DDL中提取列的中文名和注释"""
        ddl = self._get_table_ddl(table_name)
        pattern = re.compile(rf"{column_name}.*?--\s*\{{(.*?)\}}", re.S)
        match = pattern.search(ddl)
        if match:
            comment_json = "{" + match.group(1).strip() + "}"
            try:
                comment_data = eval(comment_json)  # 解析成字典
                return comment_data
            except SyntaxError:
                return {}
        return {}

    @staticmethod
    def extract_table_cn_name_from_ddl(ddl: str) -> Optional[str]:
        """从DDL中提取表的中文名"""
        match = re.search(r'--\s*\{"表中文名":\s*"([^"]+)"', ddl)
        if match:
            return match.group(1)
        return None

    def _get_sample_data(self, table_name: str, limit: int = 5) -> Dict[str, List[Any]]:
        """获取表中每列的示例数据"""
        query = f"SELECT * FROM {table_name} LIMIT {limit};"
        cursor = self.conn.cursor()
        try:
            cursor.execute(query)
            rows = cursor.fetchall()
            col_names = [description[0] for description in cursor.description]
        except sqlite3.Error as e:
            print(f"Error fetching sample data for table {table_name}: {e}")
            return {}
        finally:
            cursor.close()

        # 转换数据为 {列名: [示例数据]}
        sample_data = {col: [] for col in col_names}
        for row in rows:
            for col, value in zip(col_names, row):
                sample_data[col].append(value)

        return sample_data

    def _get_column_enums(self,
                          table_name: str,
                          column_name: str,
                          column_type: str,
                          limit_ratio: float = 1) -> Optional[List[Any]]:
        """获取列的枚举值（当枚举值数量少于总数据量的 limit_ratio 时，或类型为非数值型时）"""
        cursor = self.conn.cursor()

        total_count_query = f'SELECT COUNT(*) FROM "{table_name}";'
        distinct_count_query = f'SELECT COUNT(DISTINCT "{column_name}") FROM "{table_name}";'

        try:
            cursor.execute(total_count_query)
            total_count = cursor.fetchone()[0]

            cursor.execute(distinct_count_query)
            distinct_count = cursor.fetchone()[0]

            is_numeric = column_type.lower() in ['integer', 'real', 'numeric', 'float', 'double']

            if total_count == 0 or distinct_count > total_count * limit_ratio:
            # if is_numeric or (total_count == 0 or distinct_count > total_count * limit_ratio):
                return None

            values_query = f'SELECT DISTINCT "{column_name}" FROM "{table_name}";'
            cursor.execute(values_query)
            enums = [row[0] for row in cursor.fetchall()]

        except sqlite3.OperationalError as e:
            enums = None
        finally:
            cursor.close()
        return enums

    def initialize_data(self):
        """将 SQLite 中存放的数据转换为 TableInfo 和 ColumnInfo 的形式"""
        table_names = self._get_all_sqlite_table_names()   # 获取该SQLITE中所有的表名（英文）

        for table_name in table_names:
            ddl = self._get_table_ddl(table_name)    # 获取表的DDL

            # 如果是中文环境，尝试提取中文表名；否则不处理中文名
            table_cn_name = None
            if self.language == 'cn':
                table_cn_name = self.extract_table_cn_name_from_ddl(ddl)

            columns = self._get_column_info(table_name)  # 获取列信息

            # 创建 TableInfo 对象
            table_info = TableInfo(
                name_en=table_name,
                name_cn=table_cn_name if self.language == 'cn' else None,
                columns=columns,
                ddl=ddl
            )

            # 将 TableInfo 对象添加到 self.tables 列表中
            self.tables.append(table_info)
        self._transfer_tables_into_dfs_infos()  # 将表信息和数据转换为DataFrame并存储到self.infos和self.dfs

    def _ensure_connection(self):
        """确保数据库连接是活跃的"""
        try:
            # 测试连接
            self.conn.execute("SELECT 1")
        except (sqlite3.Error, AttributeError):
            # 如果连接已关闭或出错，重新建立连接
            self.conn = sqlite3.connect(self.path, check_same_thread=False)

    async def async_execute(self, sql: str = None) -> Optional[pd.DataFrame]:
        """
        这里使用异步之后，可能会出现数据库关闭的情况.
        """
        try:
            self._ensure_connection()  # 确保连接存在
            res = await asyncio.to_thread(lambda: pd.read_sql(sql, self.conn))
            return res
        except sqlite3.Error as error:
            printf(color='red', title='Sqlite执行失败', message=f"Error executing SQL: {error}")
            raise error

    def execute(self, sql: str = None) -> Optional[pd.DataFrame]:
        try:
            self._ensure_connection()  # 确保连接存在
            res = pd.read_sql(sql, self.conn)
            return res
        except sqlite3.Error as error:
            printf(color='red', title='Sqlite执行失败', message=f"Error executing SQL: {error}")
            raise error

    def _get_all_tables(self):
        """获取该数据库中所有表"""
        cursor = self.conn.cursor()
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        all_tables = []
        for table in tables:
            all_tables.append(table[0])
        return all_tables

    def export_table_to_csv(self, table_name: str, file_path: str) -> None:
        """
        导出指定表的数据到 CSV 文件。
        Args:
            table_name: 表名
            file_path: 导出文件路径
        """
        query = f"SELECT * FROM {table_name};"
        df = pd.read_sql(query, self.conn)
        df.to_csv(file_path, index=False)
        print(f"表 {table_name} 已导出到 {file_path}")

    def __getstate__(self):
        """自定义序列化时的状态"""
        state = self.__dict__.copy()
        # 移除无法序列化的属性
        # 删除数据库连接
        state['conn'] = None
        # 保留本地枚举值
        state['local_enums'] = self.enum_manager.local_store

        # 如果有ES，则删除与ES客户端连接相关的属性
        if state['es_client'] is not None:
            state['es_client'] = None
            state['enum_manager'] = None
            state['knowledge'] = None

        return state

    def __setstate__(self, state):
        """反序列化后重建不可序列化的属性"""
        self.__dict__.update(state)
        # 在反序列化时重新建立数据库连接
        self.conn = sqlite3.connect(self.path, check_same_thread=False)
        # 在反序列化的时候重新建立ES连接
        if self.es_config != {}:
            try:
                self.es_client = ElasticsearchClient(**self.es_config)
            except Exception as e:
                print(e)
            self._initialize_knowledge(self.es_client, self.embedding_model)
            self.enum_manager.local_store = self.local_enums


if __name__ == '__main__':
    pass
