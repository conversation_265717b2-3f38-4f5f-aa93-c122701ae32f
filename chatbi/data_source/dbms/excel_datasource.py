import pandas as pd
import os
import sqlite3
from typing import List
from chatbi.data_source.dbms.sqlite_datasource import SqliteDataSource
from chatbi.utils.temporary_file_manager import TempFileManager
from chatbi.utils.printf import printf


class ExcelDataSource(SqliteDataSource):
    type = 'data_table'
    database = 'excel'

    def __init__(self,
                 source: str,
                 data_path: str,
                 sqlite_path: str = None,
                 language: str = 'cn',
                 embedding_model=None,
                 es_client=None,
                 source_description: str = None,
                 ):
        """
        初始化 DataTableDataSource
        Args:
            data_path: Excel 和 CSV 文件所在目录的路径
            language: 数据语言，默认为中文('cn')
        """
        self.directory_path = data_path

        if sqlite_path is None:
            self.temp_manager = TempFileManager()                                           # 临时文件管理器
            self.file_name = f"{source}.sqlite"
            self.sqlite_file = self.temp_manager.create_temp_file(self.file_name)           # 创建一个临时文件
            self.sqlite_path = os.path.join(self.temp_manager.get_path(), self.file_name)
        else:
            self.sqlite_path = sqlite_path

        # 检查并创建新的 SQLite 文件连接
        self._create_sqlite_database()
        self.initialize_data_from_files()

        super().__init__(language=language,
                         source=source,
                         es_client=es_client,
                         embedding_model=embedding_model,
                         sqlite_path=self.sqlite_path,
                         source_description=source_description)

        self.enhance_date_detection()

    def _create_sqlite_database(self):
        """
        创建一个新的 SQLite 数据库文件，如果文件已经存在则清空它
        """
        if os.path.exists(self.sqlite_path):
            try:
                os.remove(self.sqlite_path)
                print(f"现有的 SQLite 文件已被移除：{self.sqlite_path}")
            except OSError as e:
                print(f"删除 SQLite 文件时发生错误: {e}")

        try:
            self.conn = sqlite3.connect(self.sqlite_path)
            print(f"SQLite 数据库已成功创建并连接 {self.sqlite_path}")
        except sqlite3.Error as e:
            print(f"创建 SQLite 数据库时发生错误: {e}")

    def _get_data_files(self) -> List[str]:
        """
        获取指定目录下的所有 Excel 和 CSV 文件路径。
        Returns: 文件路径列表。
        """
        data_files = []

        if self.directory_path:
            # 如果是单个文件路径，直接判断并添加
            if not os.path.exists(self.directory_path):
                raise FileNotFoundError(f"路径 {self.directory_path} 不存在.")

            if os.path.isfile(self.directory_path):
                if self.directory_path.endswith('.xlsx') or self.directory_path.endswith('.xls') or self.directory_path.endswith('.csv'):
                    data_files.append(self.directory_path)
            # 如果是目录，列出其中的所有文件
            elif os.path.isdir(self.directory_path):
                for file in os.listdir(self.directory_path):
                    if file.endswith('.xlsx') or file.endswith('.xls') or file.endswith('.csv'):
                        data_files.append(os.path.join(self.directory_path, file))
        return data_files

    @staticmethod
    def _read_data(file_path: str) -> pd.DataFrame:
        """
        读取 Excel 或 CSV 文件中的数据表。
        Args:
            file_path: 文件路径。
        Returns: 包含数据的 DataFrame。
        """
        try:
            if file_path.endswith('.csv'):
                data = pd.read_csv(file_path)  # 读取 CSV 文件
            else:
                data = pd.read_excel(file_path)  # 读取 Excel 文件
            return data
        except Exception as e:
            print(f"读取文件 {file_path} 时发生错误: {e}")
            return pd.DataFrame()

    def _generate_create_table_sql(self, table_name: str, df: pd.DataFrame) -> str:
        """
        生成SQLite的建表语句，并将字段中文名作为注释保存。
        Args:
            table_name: 表名。
            df: 包含数据的 DataFrame。
        Returns: 生成的建表语句。
        """
        columns = []
        table_en_name = table_name.upper()

        for column in df.columns:
            # 默认所有列类型为 TEXT
            col_type = 'TEXT'
            # 为每个列添加注释
            col_comment = f'-- {{"字段中文": "{column}"}}'
            column_def = f'{column} {col_type}, {col_comment}'
            columns.append(column_def)

        # 把最后一行去掉第一个逗号，避免SQLite语法错误
        last_column = columns.pop()
        last_column = last_column.replace(',', '')
        columns.append(last_column)

        # 拼接建表语句
        create_table_sql = f'CREATE TABLE {table_en_name} ('

        # 拼接字段定义部分
        columns_str = "\n    ".join(columns)
        create_table_sql += f'\n    {columns_str}\n);'

        return create_table_sql

    def _create_table_and_insert_data(self, table_name: str, df: pd.DataFrame):
        """
        创建表并插入数据到 SQLite。
        Args:
            table_name: 表名。
            df: 包含数据的 DataFrame。
        """
        create_table_sql = self._generate_create_table_sql(table_name, df)

        try:
            self.conn.execute(create_table_sql)  # 执行建表语句
            print(f"数据表: {table_name} 成功创建。")

            # 插入数据
            df.to_sql(table_name, self.conn, index=False, if_exists='append')
            print(f"数据已成功插入至: {table_name}")
        except Exception as e:
            # print(f"创建数据表或插入数据时错误: {e}")
            printf(color='red', title=f'创建数据表或插入数据时错误: {e}', message=f'{create_table_sql}')

    def initialize_data_from_files(self):
        """
        从 Excel 和 CSV 文件中初始化数据，将每个文件转换为 SQLite 中的表。
        """
        data_files = self._get_data_files()
        for file_path in data_files:
            df = self._read_data(file_path)
            if df.empty:
                print(f"文件 {file_path} 无法读取或为空，跳过。")
                continue

            # 文件名为表名
            table_name = os.path.splitext(os.path.basename(file_path))[0].upper()

            # 创建表并插入数据
            self._create_table_and_insert_data(table_name, df)


if __name__ == '__main__':
    path = '/Users/<USER>/Code/test-chatbi/江苏2025'
    eds = ExcelDataSource(data_path=path,  source='test_excel')




