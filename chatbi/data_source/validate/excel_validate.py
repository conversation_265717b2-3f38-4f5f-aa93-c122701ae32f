import pandas as pd
import re


class ExcelValidationError(Exception):
    """自定义异常类，用于 Excel 规范检查"""
    pass

def is_valid_sql_column_name(name):
    """检查列名是否适合 SQLite"""
    if not isinstance(name, str) or not name.strip():
        return False
    # SQLite 允许中文、字母、数字、下划线，但不能以数字开头
    # 禁止特定非法字符（* ? : [] {} 等）
    if name[0].isdigit():
        return False
    illegal_chars = r'[\*\?\:\[\]\{\}]'
    return not bool(re.search(illegal_chars, name))

def clean_column_name(name):
    """处理列名：替换中文括号，数字开头加 column_ 前缀"""
    if not isinstance(name, str) or not name.strip():
        raise ExcelValidationError(f"列名 '{name}' 为空或非法")

    original_name = name
    # 替换中文括号为英文括号
    name = name.replace('（', '(').replace('）', ')')
    name = name.replace('(', '_').replace(')', '')
    # 替换非法字符（* ? : [] {} 等）为下划线
    name = re.sub(r'[\*\?\:\[\]\{\}]', '_', name)
    # 清理多余下划线
    name = re.sub(r'_+', '_', name).strip('_')

    # 如果以数字开头，添加 'column_' 前缀
    if name and name[0].isdigit():
        name = 'column_' + name

    # 如果转换后为空，抛出错误
    if not name:
        raise ExcelValidationError(f"列名 '{original_name}' 转换后为空")

    return name

def check_merged_cells(file_path):
    """检查 Excel 文件中是否存在合并单元格（需要 openpyxl）"""
    try:
        import openpyxl
        wb = openpyxl.load_workbook(file_path)
        for sheet_name in wb.sheetnames:
            ws = wb[sheet_name]
            if ws.merged_cells.ranges:
                raise ExcelValidationError(
                    f"工作表 '{sheet_name}' 中存在合并单元格，请拆分后再处理"
                )
    except ImportError:
        print("警告：未安装 openpyxl，跳过合并单元格检查")
    except Exception as e:
        raise ExcelValidationError(f"检查合并单元格时出错: {e}")

def check_data_structure(df):
    """检查数据结构是否规范"""
    # 检查空行
    if df.isna().all(axis=1).any():
        raise ExcelValidationError("Excel 文件中包含空行")

    # 检查空列
    if df.isna().all().any():
        raise ExcelValidationError("Excel 文件中包含空列")

    # 检查列名是否为空
    if df.columns.isna().any() or any(not str(col).strip() for col in df.columns):
        raise ExcelValidationError("Excel 文件中包含空列名")

def validate_and_clean_excel(file_path, output_path=None, check_merged=True):
    """主函数：检查 Excel 规范并处理列名"""
    try:
        # 可选：检查合并单元格
        if check_merged:
            check_merged_cells(file_path)

        # 读取 Excel 文件（默认读取第一个工作表）
        df = pd.read_excel(file_path, sheet_name=0)

        # 检查数据结构
        check_data_structure(df)

        # 处理列名
        new_columns = []
        used_names = set()
        for col in df.columns:
            cleaned_name = clean_column_name(col)
            # 避免重复列名
            base_name = cleaned_name
            counter = 1
            while cleaned_name in used_names:
                cleaned_name = f"{base_name}_{counter}"
                counter += 1
            new_columns.append(cleaned_name)
            used_names.add(cleaned_name)

        # 更新 DataFrame 的列名
        df.columns = new_columns

        # 如果指定了输出路径，保存处理后的 Excel
        if output_path:
            df.to_excel(output_path, index=False)
            print(f"处理后的 Excel 文件已保存到: {output_path}")

        return df

    except ExcelValidationError as e:
        print(f"Excel 规范检查失败: {e}")
        raise
    except Exception as e:
        print(f"处理 Excel 文件时发生错误: {e}")
        raise


if __name__ == "__main__":
    input_file = "/Users/<USER>/工作/梧桐ChatBI/演示数据/多表关联测试/用户基本信息表.xlsx"
    output_file = "cleaned_example.xlsx"

    try:
        cleaned_df = validate_and_clean_excel(
            input_file,
            output_path=output_file,
            check_merged=True
        )
        print("处理后的列名:")
        print(cleaned_df.columns.tolist())
    except Exception as e:
        print("程序终止")