"""
Date Detection
用于自动识别数据中的时间相关列，并抽取出时间格式
Tian 20241212
"""

from typing import List, Dict, Any, Optional, Tuple, Set, Pattern
import re
from datetime import datetime
import pandas as pd
from collections import defaultdict, Counter
from enum import Enum, auto

from chatbi.utils.printf import printf


class DatePatternType(Enum):
    YEAR_MONTH = auto()
    YEAR_MONTH_DAY = auto()
    DATETIME = auto()
    TIMESTAMP = auto()
    UNKNOWN = auto()


class DatePatternDetector:

    DATE_COLUMN_PATTERNS = {
        re.compile(r'.*(date|日期).*', re.I): DatePatternType.YEAR_MONTH_DAY,
        re.compile(r'.*(datetime|时间).*', re.I): DatePatternType.DATETIME,
        re.compile(r'.*timestamp.*', re.I): DatePatternType.TIMESTAMP,
        re.compile(r'.*(yyyymm|年月).*', re.I): DatePatternType.YEAR_MONTH
    }

    DATE_FORMATS = {
        DatePatternType.YEAR_MONTH: [
            '%Y%m',
            '%Y-%m',
            '%Y/%m'
        ],
        DatePatternType.YEAR_MONTH_DAY: [
            '%Y%m%d',
            '%Y-%m-%d',
            '%Y/%m/%d'
        ],
        DatePatternType.DATETIME: [
            '%Y-%m-%d %H:%M:%S',
            '%Y/%m/%d %H:%M:%S',
            '%Y%m%d%H%M%S'
        ],
        DatePatternType.TIMESTAMP: [
            '%Y-%m-%d %H:%M:%S.%f',
            '%Y%m%d%H%M%S%f'
        ]
    }

    @classmethod
    def is_valid_date_value(cls, value: str) -> bool:
        """
        检测是否为有效的日期格式
        """
        if not isinstance(value, str):
            return False

        # 至少是YYYYMM的格式
        if len(value) < 6:
            return False

        if not any(c.isdigit() for c in value):
            return False

        # 常用日期符号检测
        separators = {'-', '/', '.', ' '}
        if any(sep in value for sep in separators):

            parts = re.split(r'[-/\s.]', value)
            return all(part.isdigit() for part in parts)

        return value.isdigit()

    @classmethod
    def detect_date_type_from_name(cls, column_name: str) -> Optional[DatePatternType]:

        for pattern, date_type in cls.DATE_COLUMN_PATTERNS.items():
            if pattern.match(column_name):
                return date_type
        return None

    @classmethod
    def detect_date_format(cls, value: str) -> Tuple[Optional[str], Optional[DatePatternType]]:

        if not cls.is_valid_date_value(str(value)):
            return None, None

        for pattern_type, formats in cls.DATE_FORMATS.items():
            for fmt in formats:
                try:
                    parsed_date = datetime.strptime(str(value), fmt)

                    if 1900 <= parsed_date.year <= 2100:
                        return fmt, pattern_type
                except ValueError:
                    continue

        return None, None

    @classmethod
    def analyze_date_patterns(cls, values: List[str]) -> Tuple[Optional[str], Optional[DatePatternType]]:

        format_counts = Counter()
        type_counts = Counter()

        valid_values = [str(v) for v in values if cls.is_valid_date_value(str(v))]

        if not valid_values:
            return None, None

        min_required_matches = max(2, len(valid_values) * 0.3)

        for value in valid_values:
            fmt, pattern_type = cls.detect_date_format(value)
            if fmt and pattern_type:
                format_counts[fmt] += 1
                type_counts[pattern_type] += 1

        if not format_counts or format_counts.most_common(1)[0][1] < min_required_matches:
            return None, None

        most_common_format = format_counts.most_common(1)[0][0]
        most_common_type = type_counts.most_common(1)[0][0]

        return most_common_format, most_common_type


class DateDetectionEnhancer:

    def __init__(self, data_source: 'DataSource'):
        self.data_source = data_source
        self.detector = DatePatternDetector()

    def enhance_date_information(self) -> Dict[str, List[Dict[str, Any]]]:

        detection_results = defaultdict(list)

        for table in self.data_source.tables:
            table_results = self._enhance_table_date_info(table)
            if table_results:
                detection_results[table.name_en] = table_results

        return dict(detection_results)

    def _enhance_table_date_info(self, table: 'TableInfo') -> List[Dict[str, Any]]:

        results = []

        for column in table.columns:
            result = self._enhance_column_date_info(table.name_en, column)
            if result:
                results.append(result)

        return results

    def _enhance_column_date_info(self, table_name: str, column: 'ColumnInfo') -> Optional[Dict[str, Any]]:

        if column.is_datetime:
            return None

        if any(keyword in column.name_en.lower() for keyword in ['code', 'id', 'num', 'count', 'amt', 'age']):
            return None

        detection_result = {
            'column_name': column.name_en,
            'original_type': column.col_type,
            'detection_method': None,
            'detected_format': None,
            'pattern_type': None
        }

        enum_values = self.data_source.enum_manager.get_enums_by_column(
            table_name_en=table_name,
            column_name_en=column.name_en
        )
        if enum_values:
            format_str, pattern_type = self.detector.analyze_date_patterns(enum_values)
            if format_str and pattern_type:
                detection_result.update(self._process_enum_detection(column, format_str, pattern_type))
                return detection_result

        if column.example_data:
            format_str, pattern_type = self.detector.analyze_date_patterns(
                [str(x) for x in column.example_data if x is not None]
            )
            if format_str and pattern_type:
                detection_result.update(self._process_example_detection(column, format_str, pattern_type))
                return detection_result

        name_pattern_type = self.detector.detect_date_type_from_name(column.name_en)
        if name_pattern_type and any(keyword in column.name_en.lower() for keyword in ['date', 'time', '日期', '时间']):
            detection_result.update(self._process_name_detection(column, name_pattern_type))
            return detection_result

        return None

    def _process_name_detection(self,
                                column: 'ColumnInfo',
                                pattern_type: DatePatternType) -> Dict[str, Any]:

        column.is_datetime = True
        column.datetime_pattern = self.detector.DATE_FORMATS[pattern_type][0]
        return {
            'detection_method': 'column_name',
            'detected_format': column.datetime_pattern,
            'pattern_type': pattern_type.name
        }

    def _process_enum_detection(self,
                                column: 'ColumnInfo',
                                format_str: str,
                                pattern_type: DatePatternType) -> Dict[str, Any]:

        column.is_datetime = True
        column.datetime_pattern = format_str
        return {
            'detection_method': 'enum_values',
            'detected_format': format_str,
            'pattern_type': pattern_type.name
        }

    def _process_example_detection(self,
                                   column: 'ColumnInfo',
                                   format_str: str,
                                   pattern_type: DatePatternType) -> Dict[str, Any]:

        column.is_datetime = True
        column.datetime_pattern = format_str
        return {
            'detection_method': 'example_data',
            'detected_format': format_str,
            'pattern_type': pattern_type.name
        }
