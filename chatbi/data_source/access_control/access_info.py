from pydantic import BaseModel
from typing import Dict, Any, Callable


class AccessInfo(BaseModel):
    permissions: Dict[str, Any] = {}

    def __init__(self, permissions: Dict[str, Any] = None, **data):
        if permissions is None:
            permissions = {}
        super().__init__(permissions=permissions, **data)

    def add_permission(self, key: str, value: Any):
        """动态添加权限字段"""
        self.permissions[key] = value

    def remove_permission(self, key: str):
        """删除权限字段"""
        if key in self.permissions:
            del self.permissions[key]

    def has_permission(self, key: str, value: Any) -> bool:
        """检查某个权限字段是否包含指定的值"""
        return self.permissions.get(key) == value

    def validate(self, validator: Callable[[Dict[str, Any]], bool]) -> bool:
        """通用验证方法，通过自定义函数检查权限"""
        return validator(self.permissions)

    def to_dict(self) -> Dict[str, Any]:
        return self.model_dump()

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "AccessInfo":
        """从字典加载权限"""
        return cls(permissions=data)
