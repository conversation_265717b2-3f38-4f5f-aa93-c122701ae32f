from chatbi.data_source.access_control.access_info import AccessInfo
from typing import Optional


def validate_access(
        input_access: Optional[AccessInfo] = None,
        required_access: Optional[AccessInfo] = None) -> bool:
    """
    验证权限

    :param input_access: 用户的权限信息
    :param required_access: 要求的权限信息
    :return: 是否通过权限验证
    """
    if required_access is None:
        return True

    if input_access is None:
        return False

    input_permissions = input_access.permissions
    required_permissions = required_access.permissions

    # 检查输入权限是否包含所有要求规定的字段
    for key, required_values in required_permissions.items():
        user_value = input_permissions.get(key)

        # 将单值统一转换为列表，便于统一处理
        if not isinstance(required_values, list):
            required_values = [required_values]
        if not isinstance(user_value, list):
            user_value = [user_value] if user_value is not None else []

        # 验证用户权限字段是否满足要求
        if not any(value in required_values for value in user_value):
            return False

    return True


if __name__ == '__main__':
    # 示例：定义用户权限
    user_permissions = AccessInfo(permissions={"role": ["admin"], "location": "US", "department": "IT"})

    # 示例：定义要求的权限
    required_permissions = AccessInfo(permissions={"role": ["user", "admin"], "time": ["xx"]})

    # 打印权限信息
    print("User Permissions:", user_permissions.permissions)
    print("Required Permissions:", required_permissions.permissions)

    # 验证权限
    if validate_access(input_access=user_permissions, required_access=required_permissions):
        print("Access Granted")
    else:
        print("Access Denied")
