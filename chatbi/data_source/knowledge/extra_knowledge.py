"""
为生成SQL提供额外的补充信息。
目前仅支持ES引擎
"""

from typing import List, Optional, Tuple
from chatbi.rag.embedding.embedding import EmbeddingModel
from chatbi.rag.storage.store_in_es import ElasticSearchVectorStore
from chatbi.rag.storage.store_in_memory import InMemoryVectorStore
from chatbi.rag.storage.base import VectorStoreBase
from chatbi.data_source.knowledge.knowledge_chunk import SQLKnowledgeChunk, Chunk
from chatbi.es.es_client import ElasticsearchClient
from chatbi.utils.printf import printf


class ExtraKnowledge:
    def __init__(self,
                 source: str = None,
                 es_client: Optional[ElasticsearchClient] = None,
                 embedding_model: Optional[EmbeddingModel] = None,
                 ):

        """
        初始化 ExtraKnowledge
        Args:
            source (str): 数据来源标识
            es_client (Optional[ElasticsearchClient]): ElasticSearch 客户端
            embedding_model (Optional[BaseEmbedding]): 嵌入模型
        """

        self.esk_source = source
        self.embedding_model = embedding_model
        self.store: Optional[VectorStoreBase] = None

        if self.esk_source:
            self.esk_source = "EXTRA_KNOWLEDGE_" + self.esk_source

        if embedding_model is None:
            printf(color='yellow', title='ExtraKnowledge 初始化失败',
                   message='请配置Embedding Model')
            return

        if es_client:
            # 使用 ElasticSearch 实现
            self.store = ElasticSearchVectorStore(
                source=self.esk_source,
                es_client=es_client,
                embedding_model=embedding_model
            )
            printf(color='green', title='ExtraKnowledge 已初始化', message='使用 ElasticSearch 存储实现')
        else:
            # 使用本地内存实现
            self.store = InMemoryVectorStore(
                source=self.esk_source,
                embedding_model=embedding_model
            )
            printf(color='green', title='ExtraKnowledge 已初始化', message='使用本地内存存储实现')

    def add_extra_knowledge(self, knowledge_chunks: List[Chunk]):
        """
        添加知识块到存储中
        Args:
            knowledge_chunks (List[SQLKnowledgeChunk]): 知识块列表
        """
        if not self.store:
            printf(color='red', title='存储未初始化', message='无法添加知识块')
            return

        for chunk in knowledge_chunks:
            try:
                self.store.add_chunk(chunk)
                print(f"已添加知识块: {chunk.chunk_id}")
            except Exception as e:
                print(f"添加知识块 {chunk.chunk_id} 时出错: {e}")

    def search_knowledge(self, query: str, top_k: int = 2) -> Optional[List[Chunk]]:
        """
        检索最相关的知识块
        Args:
            query (str): 查询问题
            top_k (int, optional): top k
        Returns:
            List[SQLKnowledgeChunk]: 检索出来的知识块列表
        """
        if not self.store:
            printf(color='red', title='存储未初始化', message='无法检索知识块')
            return []

        try:
            # 检索
            results_tmp: List[Tuple[Chunk, float]] = self.store.search_chunks(query=query,
                                                                              top_k=top_k)

            results: List[Chunk] = [res[0] for res in results_tmp]

            knowledge_results = []

            for chunk in results:
                if isinstance(chunk, Chunk):
                    knowledge_results.append(chunk)
            return knowledge_results

        except Exception as e:
            print(f"检索知识块时出错: {e}")
            return []

    def get_all_knowledge(self):
        return self.store.get_all_knowledge()

    def delete_knowledge(self, chunk_id: str):
        """
        删除知识
        Returns:
        """
        if not self.store:
            printf(color='red', title='存储未初始化', message='无法删除知识块')
            return

        try:
            self.store.delete_chunk(chunk_id=chunk_id)
            print(f"已删除知识块: {chunk_id}")
        except Exception as e:
            print(f"删除知识块 {chunk_id} 时出错: {e}")
        pass


if __name__ == '__main__':
    from chatbi.rag.embedding.embedding import EmbeddingModel
    from chatbi.es.es_client import ElasticsearchClient

    from chatbi.rag.chunk import Chunk
    from chatbi.data_source.knowledge.knowledge_chunk import SQLKnowledgeChunk

    ck1 = Chunk(content='优秀青年人才', metadata={'sql': 'xxx'})

    sck1 = SQLKnowledgeChunk(content='优秀青年人才', sql='select xx')

    # 配置
    # es_cli = ElasticsearchClient(
    #     host="************",
    #     port=9200,
    #     request_timeout=10
    # )

    # embedding model
    apikey = "sk-68ac5f5ccf3540ba834deeeaecb48987"
    embd_model = EmbeddingModel(api_key=apikey)

    e = ExtraKnowledge(embedding_model=embd_model,
                       # es_client=es_cli,
                       source='test')

    e.add_extra_knowledge([ck1, sck1])

    e.search_knowledge(query='青年')

    # e.search_knowledge(query='x')
