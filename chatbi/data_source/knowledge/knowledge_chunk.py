"""
用于管理额外知识，用于SQL生成中的逻辑、现有NL2SQL示例的召回。
定义一个基础的知识块数据结构；
"""

from typing import Optional, List, Dict
import uuid
from chatbi.rag.chunk import Chunk


class SQLKnowledgeChunk(Chunk):
    def __init__(self,
                 content: str,
                 chunk_id: Optional[str] = None,
                 sql: Optional[str] = None,
                 explain: Optional[str] = None,
                 embedding: Optional[List[float]] = None,
                 embedding_type: Optional[str] = None,
                 source: Optional[str] = None):

        if chunk_id is None:
            chunk_id = str(uuid.uuid4())[:8]

        meta = {'sql': sql, 'explain': explain}

        super().__init__(chunk_id=chunk_id,
                         content=content,
                         metadata=meta,
                         embedding=embedding,
                         embedding_type=embedding_type,
                         source=source)

        self.sql = sql
        self.explain = explain

    # def __repr__(self):
    #     return (f"KnowledgeChunk(ID: {self.chunk_id}, Content: {self.content[:20]}, "
    #             f"SQL: {self.sql}, Explain: {self.explain})")


if __name__ == '__main__':
    kc = SQLKnowledgeChunk(content='测试', sql='xxx', explain='yyy')
    print(kc.to_json())
    print(kc)
