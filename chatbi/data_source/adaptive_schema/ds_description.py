"""
Dynamic Adaptive Schemas Prompt
"""

from typing import Optional, Dict, List, Set
from dataclasses import dataclass
from collections import defaultdict
from tqdm import tqdm
import json
from datetime import datetime, timedelta


@dataclass
class SchemaContext:
    query: Optional[str]
    target_table: Optional[str]
    max_enum_examples: int = 5
    min_retention_score: float = 0.3
    include_foreign_keys: bool = True
    include_descriptions: bool = True
    include_date_info: bool = True
    date_examples_count: int = 3


class DateExampleGenerator:

    @staticmethod
    def generate_examples(date_format: str, count: int = 3) -> List[str]:

        try:

            today = datetime.now()
            dates = [
                today,
                today - timedelta(days=30),
                today + timedelta(days=30),
            ]

            formatted_dates = []
            for date in dates[:count]:
                try:
                    formatted_dates.append(date.strftime(date_format))
                except ValueError:
                    continue

            return formatted_dates

        except Exception:
            # Fallback for complex or unsupported formats
            if '%Y' in date_format:
                return ['20240101', '20240201', '20240301']
            elif '%y' in date_format:
                return ['240101', '240201', '240301']
            return []


class SchemaBuilder:
    """Helper class for building schema strings"""
    def __init__(self):
        self.parts: List[str] = []
        self.indent_level = 0
        self.indent_str = "  "

    def indent(self) -> None:
        self.indent_level += 1

    def dedent(self) -> None:
        self.indent_level = max(0, self.indent_level - 1)

    def add_line(self, line: str = "") -> None:
        if line:
            self.parts.append(f"{self.indent_str * self.indent_level}{line}")
        else:
            self.parts.append("")

    def build(self) -> str:
        return "\n".join(self.parts)


class AdaptiveSchemaGenerator:
    """Enhanced schema generator with improved structure and features"""

    def __init__(self, data_source: 'DataSource'):
        self.data_source = data_source
        self.foreign_keys_cache: Dict[str, List[str]] = {}
        self.date_example_generator = DateExampleGenerator()

    def generate_schema(self,
                        query: Optional[str] = None,
                        target_table: Optional[str] = None) -> str:
        """
        Generate an adaptive schema based on the query context

        Args:
            query: Optional user query for context-aware enum selection
            target_table: Optional specific table to generate schema for

        Returns:
            Formatted schema string
        """
        context = SchemaContext(query=query, target_table=target_table)
        builder = SchemaBuilder()

        # Generate schema for tables
        self._generate_tables_schema(context, builder)

        # Add foreign key information if needed
        if context.include_foreign_keys:
            self._add_foreign_keys_info(builder)

        # Add date columns summary if needed
        if context.include_date_info:
            self._add_date_columns_summary(builder)

        return builder.build()

    def _build_column_parts(self,
                            table: 'TableInfo',
                            column: 'ColumnInfo',
                            context: SchemaContext) -> List[str]:
        """Build the parts of a column definition"""
        parts = []

        # Basic column info
        column_name = self._get_display_column_name(column)
        parts.append(f"({column.name_en}:{column_name}")
        parts.append(column.col_type)

        # Add date information if available
        if column.is_datetime and column.datetime_pattern:
            parts.append(f"Date Format: {column.datetime_pattern}")

            # First try to get actual examples from data
            date_examples = self._get_actual_date_examples(table, column, context.date_examples_count)

            # If no actual examples, generate some
            if not date_examples:
                date_examples = self.date_example_generator.generate_examples(
                    column.datetime_pattern,
                    context.date_examples_count
                )

            if date_examples:
                parts.append(f"Example: [{', '.join(date_examples)}]")
        else:
            # Add relevant enum examples for non-date columns
            enum_examples = self._get_relevant_enums(table, column, context)
            if enum_examples:
                parts.append(f"Example: [{', '.join(map(str, enum_examples))}]")

        if context.include_descriptions and column.col_comment:
            # 这里避免很多应用把英文名复制成注释
            if column.col_comment != column.name_cn:
                parts.append(f"{column.col_comment}")

        return parts

    def _add_date_columns_summary(self, builder: SchemaBuilder) -> None:
        """Add summary of date columns and their formats"""
        date_columns = self._collect_date_columns()
        if date_columns:
            builder.add_line("\n[date_columns]")
            for table_name, columns in date_columns.items():
                builder.add_line(f"{table_name}:")
                builder.indent()
                for col_info in columns:
                    builder.add_line(
                        f"{col_info['column_name']}: "
                        f"Format={col_info['format']}, "
                        f"Type={col_info['col_type']}"
                    )
                builder.dedent()

    def _collect_date_columns(self) -> Dict[str, List[Dict[str, str]]]:
        """Collect information about date columns from all tables"""
        date_columns = defaultdict(list)

        for table in self.data_source.tables:
            for column in table.columns:
                if column.is_datetime:
                    date_columns[table.name_en].append({
                        'column_name': column.name_en,
                        'format': column.datetime_pattern or 'Unknown',
                        'col_type': column.col_type
                    })

        return dict(date_columns)

    def _generate_tables_schema(self, context: SchemaContext, builder: SchemaBuilder) -> None:
        """Generate schema for all relevant tables"""
        table_iterator = tqdm(self.data_source.tables, desc="Generating Adaptive Schema", unit="table")

        for table in table_iterator:
            if context.target_table and table.name_en.upper() != context.target_table.upper():
                continue

            self._add_table_header(table, builder)
            if table.description:
                builder.add_line(f"table description: {table.description}")
            self._add_table_columns(table, context, builder)

    def _add_table_header(self, table: 'TableInfo', builder: SchemaBuilder) -> None:
        """Add table header information to schema"""
        table_name = self._get_display_table_name(table)
        builder.add_line(f"{table.name_en}: {table_name}")
        builder.add_line("[")
        builder.indent()

    def _add_table_columns(self,
                           table: 'TableInfo',
                           context: SchemaContext,
                           builder: SchemaBuilder) -> None:
        """Add column information for a table"""
        date_columns = [col for col in table.columns if col.is_datetime]
        regular_columns = [col for col in table.columns if not col.is_datetime]

        # First add date columns
        for column in date_columns:
            column_parts = self._build_column_parts(table, column, context)
            builder.add_line(", ".join(column_parts) + "),")

        # Then add regular columns
        for column in regular_columns:
            column_parts = self._build_column_parts(table, column, context)
            builder.add_line(", ".join(column_parts) + "),")

        builder.dedent()
        builder.add_line("]")
        builder.add_line("")

    def _get_relevant_enums(self,
                            table: 'TableInfo',
                            column: 'ColumnInfo',
                            context: SchemaContext) -> List[str]:
        """Get relevant enum values based on context"""
        if context.query:
            return self.data_source.enum_manager.search_enum(
                table_name_en=table.name_en,
                column_name_en=column.name_en,
                query=context.query,
                fuzzy=True,
                top_n=context.max_enum_examples,
                method='sub_string'    # 使用子串匹配
            )
        else:
            return self.data_source.enum_manager.get_enums_by_column(
                table_name_en=table.name_en,
                column_name_en=column.name_en
            )[:context.max_enum_examples]

    def _get_display_table_name(self, table: 'TableInfo') -> str:
        """Get appropriate display name for table"""
        return table.name_cn if self.data_source.language == 'cn' and table.name_cn else table.name_en

    def _get_display_column_name(self, column: 'ColumnInfo') -> str:
        """Get appropriate display name for column"""
        return column.name_cn if self.data_source.language == 'cn' and column.name_cn else column.name_en

    def _add_foreign_keys_info(self, builder: SchemaBuilder) -> None:
        """Add foreign key relationship information"""
        foreign_keys = self._get_foreign_keys()
        if foreign_keys:
            builder.add_line("[foreign_key]")
            for fk in foreign_keys:
                builder.add_line(fk)

    def _get_foreign_keys(self) -> List[str]:
        """Get all foreign key relationships"""
        if not self.foreign_keys_cache:
            self.foreign_keys_cache = self._extract_foreign_keys()
        return self.foreign_keys_cache.get(self.data_source.source, [])

    def _extract_foreign_keys(self) -> Dict[str, List[str]]:
        """Extract and cache foreign key relationships"""
        foreign_keys = []

        for table in self.data_source.tables:
            for column in table.columns:
                if not column.is_foreign:
                    continue

                if column.ref_table and column.ref_column:
                    foreign_key = self._format_foreign_key(
                        table.name_en,
                        column.name_en,
                        column.ref_table,
                        column.ref_column
                    )
                    foreign_keys.append(foreign_key)

        return {self.data_source.source: foreign_keys}

    def _get_actual_date_examples(self,
                                  table: 'TableInfo',
                                  column: 'ColumnInfo',
                                  count: int) -> List[str]:
        """Get actual date examples from data if available"""
        examples = []

        # Try enum values first
        enum_values = self.data_source.enum_manager.get_enums_by_column(
            table_name_en=table.name_en,
            column_name_en=column.name_en
        )

        if enum_values:
            # Take the first few valid date values
            valid_dates = []
            for value in enum_values:
                if len(valid_dates) >= count:
                    break
                try:
                    # If it's already in the correct format, use it directly
                    if isinstance(value, str):
                        datetime.strptime(value, column.datetime_pattern)
                        valid_dates.append(value)
                except (ValueError, TypeError):
                    continue

            if valid_dates:
                return valid_dates[:count]

        # If no enum values, try example_data
        if column.example_data:
            valid_dates = []
            for value in column.example_data:
                if len(valid_dates) >= count:
                    break
                try:
                    if isinstance(value, str):
                        datetime.strptime(value, column.datetime_pattern)
                        valid_dates.append(value)
                except (ValueError, TypeError):
                    continue

            if valid_dates:
                return valid_dates[:count]

        return examples

    @staticmethod
    def _format_foreign_key(table: str, column: str, ref_table: str, ref_column: str) -> str:
        """Format foreign key relationship string"""
        return f"{table}.{column} = {ref_table}.{ref_column}"


if __name__ == '__main__':

    from chatbi.data_source.dbms.excel_datasource import ExcelDataSource

    excel_path = "/Users/<USER>/Code/chatbi_schemas/GUANGDONG_WG/1212"
    sqlite_path = "/Users/<USER>/Code/temp/test.sqlite"

    data = ExcelDataSource(data_path=excel_path,
                           source='test_excel',)

    generator = AdaptiveSchemaGenerator(data_source=data)

    m_schema = generator.generate_schema()


