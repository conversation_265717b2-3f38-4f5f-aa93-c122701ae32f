from typing import Dict, List, Optional
from collections import defaultdict
from chatbi.es.es_client import ElasticsearchClient
from chatbi.utils.printf import printf

from tqdm import tqdm


class EnumESManager:
    def __init__(self,
                 es_client: ElasticsearchClient,
                 source: str,
                 index_name: str = "chatbi_enums"):

        if not source:
            raise ValueError("Source cannot be empty")

        self.es_client = es_client
        self.source = source.lower()
        self.index_name = index_name

        self._has_synced_from_memory = False
        self.memory_store = None     # 引用Enumeration实例
        self._create_index()

    def _get_memory_store(self):
        """获取关联的Enumeration实例"""
        return self.memory_store

    def add_enums_es(self,
                     table_name_en: str,
                     column_name_en: str,
                     values: List[str]):
        """
        添加枚举值到ES
        Args:
            table_name_en: 表英文名
            column_name_en: 列英文名
            values: 枚举值列表
        """
        # 首次调用时，需要从内存同步数据
        if not self.es_client:
            raise ValueError("Elastic Search client must be initialized before calling add_enums_es() method")

        if not self._has_synced_from_memory:
            print('首次同步')
            memory_store = self._get_memory_store()  # 获取到内存中存放的所有枚举值数据
            if memory_store:
                self.sync_from_memory(memory_store)   # 同步至ES服务中
                self._has_synced_from_memory = True

        try:
            if not values:
                return

            values = list(set(v for v in values if v is not None and str(v).strip()))
            if not values:
                return

            # 生成文档ID source_table_column
            doc_id = self._generate_doc_id(table_name_en, column_name_en)

            # 检查文档是否存在并获取当前值
            current_doc = self.es_client.get_document(index_name=self.index_name, doc_id=doc_id)

            if current_doc:
                current_values = set(current_doc.get('values', []))
                values = list(current_values.union(set(values)))

            document = {
                "source": self.source,
                "table_name_en": table_name_en,
                "column_name_en": column_name_en,
                "values": values
            }

            self.es_client.index_document(index_name=self.index_name, doc_id=doc_id, document=document)

            printf(color='green', title=f'添加枚举值成功: {self.source}.{table_name_en}.{column_name_en}')

            # 同步ES数据回内存
            memory_store = self._get_memory_store()
            if memory_store:
                memory_store.sync_from_es(self.es_client)

        except Exception as e:
            printf(color='red', title='添加枚举值失败', message=str(e))
            raise ValueError(f"添加枚举值失败, {e}")

    def delete_enums_es(self,
                        table_name_en: str,
                        column_name_en: str,
                        values: Optional[List[str]] = None):
        """
        从ES删除枚举值
        Args:
            table_name_en: 表英文名
            column_name_en: 列英文名
            values: 要删除的枚举值列表，如果为None则删除整个字段的所有枚举值
        """
        if not self.es_client:
            raise ValueError("Elastic Search client must be initialized before calling delete_enums_es() method")

        # 首次调用时，需要从内存同步数据
        if not self._has_synced_from_memory:
            memory_store = self._get_memory_store()
            if memory_store:
                self.sync_from_memory(memory_store)
                self._has_synced_from_memory = True

        try:
            doc_id = self._generate_doc_id(table_name_en, column_name_en)

            current_doc = self.es_client.get_document(self.index_name, doc_id)

            if not current_doc:
                return

            if values is None:
                self.es_client.delete_document(index_name=self.index_name, doc_id=doc_id)

                printf(color='green', title=f'删除枚举值成功: {table_name_en}.{column_name_en}')
                memory_store = self._get_memory_store()
                if memory_store:
                    memory_store.sync_from_es(self.es_client)
                return

            current_values = set(current_doc.get('values', []))
            updated_values = list(current_values - set(values))

            if updated_values:
                document = {
                    "source": self.source,
                    "table_name_en": table_name_en,
                    "column_name_en": column_name_en,
                    "values": updated_values
                }
                self.es_client.index_document(index_name=self.index_name, doc_id=doc_id, document=document)
            else:
                self.es_client.delete_document(index_name=self.index_name, doc_id=doc_id)

            printf(color='green', title=f'删除指定枚举值成功: {table_name_en}.{column_name_en}')

            # 同步ES数据回内存
            memory_store = self._get_memory_store()
            if memory_store:
                memory_store.sync_from_es(self.es_client)

        except Exception as e:
            printf(color='red', title='删除枚举值失败', message=str(e))
            raise ValueError(f"删除枚举值失败, {e}")

    def _create_index(self):
        """创建或更新枚举值索引"""
        try:
            mapping = {
                "settings": {
                    "number_of_shards": 1,
                    "number_of_replicas": 0
                },
                "mappings": {
                    "dynamic": False,
                    "properties": {
                        "source": {"type": "keyword"},
                        "table_name_en": {"type": "keyword"},
                        "column_name_en": {"type": "keyword"},
                        "values": {
                            "type": "text",
                            "fields": {
                                "keyword": {"type": "keyword"}
                            }
                        }
                    }
                }
            }

            if not self.es_client.es.indices.exists(index=self.index_name):
                self.es_client.create_index(index_name=self.index_name, settings=mapping)
                printf(color='green', title=f'创建索引 {self.index_name} 成功')

            else:
                printf(color='yellow', title=f'索引 {self.index_name} 已存在')

        except Exception as e:
            printf(color='red', title='创建索引失败', message=str(e))
            raise

    def _generate_doc_id(self,
                         table_name_en: str,
                         column_name_en: str) -> str:
        """生成文档ID"""
        return f"{self.source}_{table_name_en}_{column_name_en}"

    def get_source_enums(self) -> Dict[str, Dict[str, List[str]]]:
        """
        获取当前 source 的所有枚举值
        Returns:
            Dict[str, Dict[str, List[str]]]: 枚举值结构
        """
        try:
            query = {"query": {"term": {"source": self.source}}, "size": 10000}
            # query = {"query": {"term": {"source": self.source}}}  # 这个地方，让我从晚上7点找bug，找到了凌晨3点... 20250117
            documents = self.es_client.search_documents(index_name=self.index_name, query=query)
            result = defaultdict(lambda: defaultdict(list))

            for doc in documents:
                table = doc['table_name_en']
                column = doc['column_name_en']
                values = doc['values']
                result[table][column] = values

            return dict(result)

        except Exception as e:
            printf(color='red', title='获取枚举值失败', message=str(e))
            return {}

    def sync_from_memory(self, memory_store):
        """
        从EnumMemoryStore同步数据到ES
        Args:
            memory_store: 内存存储实例
        """
        try:
            if self.source != memory_store.source:
                raise ValueError(f"Source mismatch: ES manager source is {self.source}, "
                                 f"but memory store source is {memory_store.source}")

            # 获取内存中的所有数据
            memory_enums: Dict[str, Dict[str, List[str]]] = memory_store.bulk_get_enums()

            # 获取ES中的现有数据
            es_enums = self.get_source_enums()

            # 合并ES和内存中的数据
            merged_enums = defaultdict(lambda: defaultdict(set))

            # 先加入内存中的数据
            for table, columns in memory_enums.items():
                for column, values in columns.items():
                    if values:  # 只处理非空值
                        # 确保values是set类型，过滤掉None和空字符串
                        values_set = {str(v) for v in values if v is not None and str(v)}
                        if values_set:  # 确保过滤后还有值
                            merged_enums[table][column].update(values_set)

            # 再加入ES中的数据
            for table, columns in es_enums.items():
                for column, values in columns.items():
                    if values:  # 只处理非空值
                        # 确保values是set类型，过滤掉None和空字符串
                        values_set = {str(v) for v in values if v is not None and str(v)}
                        if values_set:  # 确保过滤后还有值
                            merged_enums[table][column].update(values_set)

            # 计算合并后的总数据量
            total_enum_values = sum(len(values) for columns in merged_enums.values()
                                    for values in columns.values())

            printf(color='green', title='开始合并同步',
                   message=f'合并后数据规模\n表数量:{len(merged_enums)}\n枚举值总数:{total_enum_values}')

            # # 清空当前ES内容
            # self.clear_all()

            pbar = tqdm(total=total_enum_values, desc='枚举值ES客户端初始化进度')
            for table, columns in merged_enums.items():
                for column, values in columns.items():
                    if values:  # 只同步有值的数据，每个source是一个index，每一个表-列作为一个文档
                        doc_id = self._generate_doc_id(table, column)

                        document = {
                            "source": self.source,
                            "table_name_en": table,
                            "column_name_en": column,
                            "values": list(values)
                        }

                        self.es_client.index_document(
                            index_name=self.index_name,
                            doc_id=doc_id,
                            document=document
                        )

                        pbar.update(len(values))

            pbar.close()

            self._has_synced_from_memory = True
            printf(color='green', title=f'同步source {self.source} 的数据成功')

        except Exception as e:
            printf(color='red', title='同步数据失败', message=str(e))
            raise

    def clear_all(self):
        query = {"query": {"term": {"source": self.source}}, "size": 10000}
        documents = self.es_client.search_documents(index_name=self.index_name, query=query)

        for doc in documents:
            table = doc['table_name_en']
            column = doc['column_name_en']

            doc_id = self._generate_doc_id(table_name_en=table, column_name_en=column)
            self.es_client.delete_document(index_name=self.index_name, doc_id=doc_id)
        pass
