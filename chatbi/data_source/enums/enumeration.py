"""
枚举值管理器，管理数据源中的枚举类型数据

提供了一个双存储模式的枚举值管理实现：
1. 本地内存存储：快速初始化和访问，适用于只读场景
2. ES远程存储：持久化存储，适用于数据变更场景

- 支持快速初始化：初始化时只使用本地存储，无需ES连接
- 懒加载模式：只有在需要变更数据时才与ES交互
- ES优先：当启用ES存储时，以ES中的数据为准
- 自动同步：ES操作后会自动将数据同步回本地存储

示例：
1. 仅使用本地存储（快速初始化）
enum_manager = Enumeration(source='example')
enum_manager.add_enum("table", "column", ["value1", "value2"])

2. 需要持久化存储时，添加ES支持
es_client = ElasticsearchClient(host=xx, port=xx)
enum_manager = Enumeration(source='example', es_client=es_client)

# 首次调用ES操作时会自动同步本地数据
enum_manager.enum_es.add_enums_es("table", "column", ["value3"])

Attention：
1. 本地存储的add_enum和delete_enum方法只修改内存中的数据
2. 如需持久化变更，应使用enum_es.add_enums_es和delete_enums_es方法
3. ES操作会自动处理数据同步，无需手动同步
4. 当提供ES客户端时，所有数据变更都应通过ES方法进行，确保数据一致性
"""

from typing import List, Dict, Optional, Union
from collections import defaultdict

from chatbi.es.es_client import ElasticsearchClient
from chatbi.utils.printf import printf

from chatbi.similarity import (JaccardSimilarity,
                               LevenshteinSimilarity,
                               JaroWinklerSimilarity,
                               SubStringSimilarity)

EnumESManagerType = Union[None, 'EnumESManager']


class Enumeration:
    def __init__(self,
                 source: str,
                 es_client: ElasticsearchClient = None,
                 auto_load: bool = True):
        """
        初始化枚举存储
        Args:
            source: 数据源标识
            es_client: ES客户端实例（可选）
            auto_load: 是否在初始化的时候自动从ES加载数据（默认True）
        """
        self.source = source.lower()
        self.local_store = defaultdict(lambda: defaultdict(set))

        # 初始化ES管理器
        self.enum_es: EnumESManagerType = None
        if es_client is not None and isinstance(es_client, ElasticsearchClient):
            try:
                from chatbi.data_source.enums.enum_es_store import EnumESManager
                self.enum_es = EnumESManager(es_client=es_client, source=self.source)
                self.enum_es.memory_store = self

                # 自动从ES加载数据到内存
                if auto_load:
                    # printf(color='blue', title=f'正在从Elastic Search加载{self.source}的枚举数据')
                    es_data = self.enum_es.get_source_enums()

                    # 将ES数据加载到内存
                    for table, columns in es_data.items():
                        for column, values in columns.items():
                            if values:
                                self.local_store[table][column].update(values)

                    printf(color='green',
                           title=f'枚举值初始化加载完成',
                           message=f'{self.source}: 已将Elastic Search中存放的枚举值同步到内存')

            except Exception as e:
                printf(color='yellow',
                       title='从ES加载数据失败，切换至使用内存存放枚举值运行',
                       message=f'错误详情: {str(e)}')

    def bulk_get_enums(self,
                       table_name_en: Optional[str] = None) -> Dict[str, Dict[str, List[str]]]:
        """
        批量获取枚举值
        Args:
            table_name_en: 可选的表名过滤
        Returns:
            Dict格式的枚举值数据
        """
        if table_name_en:
            return {table_name_en: {k: list(v) for k, v in self.local_store[table_name_en].items()}}
        return {t: {k: list(v) for k, v in cols.items()} for t, cols in self.local_store.items()}

    def search_enum(self,
                    table_name_en: str,
                    column_name_en: str,
                    query: str,
                    top_n: int = 5,
                    fuzzy: bool = True,
                    method: str = 'sub_string') -> List[str]:
        """
        搜索枚举值，支持模糊搜索和准确搜索
        Args:
            table_name_en: 表英文名
            column_name_en: 列英文名
            query: 搜索关键词
            top_n: 返回结果数量
            fuzzy: 是否使用模糊搜索
            method: 模糊匹配方法，可使用 sub_string, jaccard, levenshtein, jaro_winkler
        Returns:
            匹配的枚举值列表
        """
        raw_values = list(self.local_store[table_name_en][column_name_en])
        # 过滤出字符串类型的值，同时过滤掉None
        values = [v for v in raw_values if isinstance(v, str) and v is not None]

        if not values:
            return []

        if fuzzy:
            return self._fuzzy_search(values=values,
                                      query=query,
                                      top_n=top_n,
                                      method=method)

        return [v for v in values if v == query]

    def get_enums_by_column(self,
                            table_name_en: str,
                            column_name_en: str) -> List[str]:
        """
        获取指定列的所有枚举值
        Args:
            table_name_en: 表英文名
            column_name_en: 列英文名
        Returns:
            枚举值列表
        """
        return list(self.local_store[table_name_en][column_name_en])

    def add_enum(self, table_name_en: str, column_name_en: str, values: List[str]):
        """
        添加枚举值到本地存储
        Args:
            table_name_en: 表英文名
            column_name_en: 列英文名
            values: 要添加的枚举值列表
        """
        if not values or not isinstance(values, List):
            values = []

        # 暂时先不用去掉数值型的
        # values = [v for v in values if not self.is_number(v) and v is not None]
        values = [v for v in values if v is not None]
        values = list(set(values))

        if table_name_en not in self.local_store:
            self.local_store[table_name_en] = defaultdict(set)
        self.local_store[table_name_en][column_name_en].update(values)

    def delete_enum(self, table_name_en: str, column_name_en: str, values: List[str]):
        """
        从本地存储删除指定的枚举值
        Args:
            table_name_en: 表英文名
            column_name_en: 列英文名
            values: 要删除的枚举值列表
        """
        if not values:
            return

        if table_name_en in self.local_store and column_name_en in self.local_store[table_name_en]:
            self.local_store[table_name_en][column_name_en].difference_update(values)

    def sync_from_es(self, es_client: ElasticsearchClient = None):
        """
        从ES同步数据到本地，主要用于ES内容发生变动的时候，自动全量同步更新至本地内存中。
        Args:
            es_client:

        Returns:

        """
        if not es_client:
            printf(color='red', title='枚举值与Elastic Search同步失败', message='Elastic Search 客户端未配置')
            raise ConnectionError('Elastic Search Client is Null')

        if not self.enum_es:
            from chatbi.data_source.enums.enum_es_store import EnumESManager
            self.enum_es = EnumESManager(es_client=es_client, source=self.source)
            self.enum_es.memory_store = self

        if self.source != self.enum_es.source:
            printf(color='red', title='数据源不匹配',
                   message=f'本地source为{self.source}，ES source为{self.enum_es.source}')
            return

        try:
            enum_data = self.enum_es.get_source_enums()  # 获取ES中的所有数据

            # 清空当前存储的数据
            self.format_enum()

            # 将ES中的数据同步到本地存储
            for table, columns in enum_data.items():
                for column, values in columns.items():
                    if table not in self.local_store:
                        self.local_store[table] = defaultdict(set)
                    self.local_store[table][column].update(values)

            printf(color='green', title='数据同步成功')

        except Exception as e:
            printf(color='red', title='数据同步失败', message=str(e))
            raise

    def format_enum(self):
        """清空所有枚举值"""
        self.local_store.clear()
        printf(color='green', title='本地存储已清空')

    def _fuzzy_search(self,
                      values: List[str],
                      query: str,
                      top_n: int,
                      method: str = 'sub_string') -> List[str]:
        """
        模糊搜索
        Args:
            values: 待搜索的枚举值列表
            query: 搜索关键词
            top_n: 返回结果数量
            method: sub_string, jaccard, levenshtein, jaro_winkler
        Returns:
            匹配度最高的top_n个结果
        """
        similarity_func = {
            'sub_string': SubStringSimilarity(),
            'jaccard': JaccardSimilarity(),
            'levenshtein': LevenshteinSimilarity(),
            'jaro_winkler': JaroWinklerSimilarity(),
        }.get(method.lower(), SubStringSimilarity())  # 默认使用子串方法

        similarity_scores = [
            (value, similarity_func.get_similarity_score(str1=query, str2=value))
            for value in values
        ]

        similarity_scores.sort(key=lambda x: x[1], reverse=True)
        return [value for value, score in similarity_scores[:top_n]]

    @staticmethod
    def is_number(s):
        """
        检查是否为数值类型
        Args:
            s: 要检查的值
        Returns:
            bool: 是否为数值
        """
        if not isinstance(s, (int, float, str)):
            return False
        try:
            float(s)
            return True
        except ValueError:
            return False

    def debug_index_content(self):
        """查看存储内容（用于调试）"""
        print("本地存储:", self.local_store)


if __name__ == "__main__":

    # 没有配置ES的情况
    enum_manager = Enumeration(source='test')

    # 测试添加枚举值到本地存储
    print("\n=== 测试添加枚举值到本地 ===")
    enum_manager.add_enum("universities", "name", ["南京大学", "广州大学", "湖南大学"])
    enum_manager.add_enum("universities", "college", ["计算机学院", "信息学院", "数学学院"])
    enum_manager.add_enum("companies", "industry", ["互联网", "金融", "教育"])

    # 测试获取枚举值
    print("\n=== 测试获取枚举值 ===")
    print("大学名称:", enum_manager.get_enums_by_column("universities", "name"))
    print("学院名称:", enum_manager.get_enums_by_column("universities", "college"))
    print("行业:", enum_manager.get_enums_by_column("companies", "industry"))

    # 如果需要ES操作，再配置ES客户端
    es_cli = ElasticsearchClient(
        host="************",
        port=9200,
        request_timeout=10
    )

    enum_manager = Enumeration(source='test', es_client=es_cli)

    print("\n=== 测试添加枚举值到本地 ===")
    enum_manager.add_enum("universities", "name", ["南京大学", "广州大学", "湖南大学"])
    enum_manager.add_enum("universities", "college", ["计算机学院", "信息学院", "数学学院"])
    enum_manager.add_enum("companies", "industry", ["互联网", "金融", "教育"])

    # 测试获取枚举值
    print("\n=== 测试获取枚举值 ===")
    print("大学名称:", enum_manager.get_enums_by_column("universities", "name"))
    print("学院名称:", enum_manager.get_enums_by_column("universities", "college"))
    print("行业:", enum_manager.get_enums_by_column("companies", "industry"))

    # 测试ES操作
    print("\n=== 测试ES操作 ===")
    # 第一次调用自动同步本地数据到ES,并且add之后将es的内容再同步回本地
    enum_manager.enum_es.add_enums_es("universities", "name", ["清华大学", "北京大学"])

    print("本地的枚举值", enum_manager.get_enums_by_column("universities", "name"))
    # 期望的输出:["南京大学", "广州大学", "湖南大学", "清华大学", "北京大学"]

    result = enum_manager.search_enum(table_name_en="universities", column_name_en="name", query='清华', fuzzy=True)

    print("模糊搜索", result)
    # 删除操作
    enum_manager.enum_es.delete_enums_es("universities", "name", ["广州大学"])

    print(enum_manager.enum_es.get_source_enums())
    # enum_manager.enum_es.clear_all()  # 清空所有枚举值
    print("\n=== 测试完成 ===")

