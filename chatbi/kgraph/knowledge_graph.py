import networkx as nx
import json
import matplotlib.pyplot as plt
from chatbi.llms.base import BaseLLM


class KnowledgeGraph:
    def __init__(self):
        self.graph = nx.DiGraph()

    def is_empty(self):
        """
        检查知识图谱是否为空
        :return: 如果图为空返回 True，否则返回 False
        """
        return self.graph.number_of_nodes() == 0 and self.graph.number_of_edges() == 0

    def add_triplet(self, entity1, relation, entity2):
        """
        添加三元组到知识图谱
        """
        self.graph.add_edge(entity1, entity2, relationship=relation)
        print(f"Added relationship: ({entity1}) -[{relation}]-> ({entity2})")

    def delete_triplet(self, entity1, relation, entity2):
        """
        删除知识图谱中三元组
        """
        if self.graph.has_edge(entity1, entity2):
            rel = self.graph.get_edge_data(entity1, entity2).get("relationship")
            if rel == relation:
                self.graph.remove_edge(entity1, entity2)
                print(f"Deleted relationship: ({entity1}) -[{relation}]-> ({entity2})")
                return
        print(f"No matching relationship found for deletion: ({entity1}) -[{relation}]-> ({entity2})")

    def view_graph(self):
        """
        可视化知识图谱
        """
        pos = nx.spring_layout(self.graph)
        nx.draw(self.graph, pos, with_labels=True, node_color="lightblue", edge_color="gray")
        edge_labels = nx.get_edge_attributes(self.graph, "relationship")
        nx.draw_networkx_edge_labels(self.graph, pos, edge_labels=edge_labels)
        plt.show()

    def query_relationships(self, entity):
        """
        查询一个实体的所有关系
        """
        if entity not in self.graph:
            print(f"Entity '{entity}' not found in the graph.")
            return []
        relationships = []
        for target in self.graph[entity]:
            relation = self.graph[entity][target]["relationship"]
            relationships.append((entity, relation, target))
        return relationships

    def export_for_llm(self, format="json"):
        """
        导出知识图谱为适合大语言模型（LLM）使用的格式
        :param format: 导出格式，支持 'json' 或 'text'
        :return: 格式化的知识图谱数据
        """
        data = []
        for entity1, entity2, edge_data in self.graph.edges(data=True):
            data.append({
                "entity1": entity1,
                "relation": edge_data.get("relationship", ""),
                "entity2": entity2
            })

        if format == "json":
            return json.dumps({"knowledge_graph": data}, indent=4, ensure_ascii=False)
        elif format == "text":
            relationships = [
                f"{item['entity1']} -[{item['relation']}]-> {item['entity2']}" for item in data
            ]
            return "\n".join(relationships)
        else:
            raise ValueError("Unsupported format. Use 'json' or 'text'.")

    def query_by_relation(self, relation):
        """
        查询具有特定关系的所有三元组
        """
        result = []
        for entity1, entity2, edge_data in self.graph.edges(data=True):
            if edge_data.get("relationship") == relation:
                result.append((entity1, relation, entity2))
        return result

    def find_shortest_path(self, entity1, entity2):
        """
        查询两个实体之间的最短路径
        """
        try:
            path = nx.shortest_path(self.graph, source=entity1, target=entity2)
            return path
        except nx.NetworkXNoPath:
            print(f"No path found between {entity1} and {entity2}")
            return []

    def get_context(self, entity, depth=2):
        """
        获取某实体的上下文信息（多跳查询）
        查询与某实体相关的所有上下文信息（实体、关系和连接路径），用于为大模型提供详细的上下文输入
        :param depth: 上下文的深度（如 1 跳、2 跳）
        """
        subgraph = nx.ego_graph(self.graph, entity, radius=depth)
        context = []
        for entity1, entity2, edge_data in subgraph.edges(data=True):
            context.append({
                "entity1": entity1,
                "relation": edge_data.get("relationship", ""),
                "entity2": entity2
            })
        return context

    def add_triplets_from_text(self, text: str, llm: BaseLLM):
        """
        从文本中智能构建知识图谱
        Args:
            text (str): 输入的自然语言文本
            llm (BaseLLM): 大语言模型对象，用于提取三元组
        """
        from chatbi.kgraph.prompts.kg_prompts import KGPrompt
        from chatbi.output_parsers.list_output_parser import ListOutputParser

        # 导出现有知识图谱的结构（如果存在），以便提供上下文
        previous = self.export_for_llm(format='json') if not self.is_empty() else ""

        # 初始化 KGPrompt 对象并设置大模型
        kg_prompt = KGPrompt()
        kg_prompt.llm = llm

        # 更新 KGPrompt 的占位符，提供知识图谱上下文和新文本
        kg_prompt.update_placeholder(previous=previous, text=text)

        chain = kg_prompt - ListOutputParser()

        # 调用大模型进行处理
        try:
            llm_response = chain.run(query=None)

            # 解析大模型的响应
            triplets = llm_response
            if not isinstance(triplets, list):
                raise ValueError("Invalid response format: expected a list of triplets.")

            # 将解析出的三元组添加到知识图谱中
            for triplet in triplets:
                if len(triplet) == 3:
                    self.add_triplet(triplet[0], triplet[1], triplet[2])
                else:
                    print(f"Invalid triplet format: {triplet}")
        except Exception as e:
            print(f"Error while processing text with LLM: {e}")


if __name__ == "__main__":
    from chatbi.llms import QwenLLM, ChatBILLM

    qwen = QwenLLM(api_key='sk-68ac5f5ccf3540ba834deeeaecb48987')

    # 初始化知识图谱
    kg = KnowledgeGraph()

    text = """
    谷歌由拉里·佩奇和谢尔盖·布林在1998年创立，总部位于加利福尼亚州山景城。
    谷歌是全球最大的搜索引擎公司，开发了包括Gmail、Android和Google Cloud等产品。
    """

    kg.add_triplets_from_text(text, llm=qwen)

    kg.export_for_llm()

    # 示例 1: 添加三元组（实体-关系-实体）
    print("\n=== 添加三元组 ===")
    kg.add_triplet(entity1="张三", relation="办理", entity2="大王卡")
    kg.add_triplet(entity1="王五", relation="办理", entity2="大王卡")
    kg.add_triplet(entity1="大王卡", relation="属于", entity2="联通公司")
    kg.add_triplet(entity1="联通公司", relation="位于", entity2="中国")
    kg.add_triplet(entity1="中国", relation="拥有", entity2="联通公司")

    # 示例 2: 查看知识图谱
    print("\n=== 查看图谱 ===")
    kg.view_graph()

    # 示例 3: 查询某个实体的所有关系
    print("\n=== 查询某实体的所有关系 ===")
    print("张三的关系:", kg.query_relationships("张三"))
    print("大王卡的关系:", kg.query_relationships("大王卡"))

    # 示例 4: 按关系类型查询
    print("\n=== 查询具有特定关系的三元组 ===")
    print("关系为 '办理' 的三元组:", kg.query_by_relation("办理"))

    # 示例 5: 查询两实体之间的最短路径
    print("\n=== 查询最短路径 ===")
    print("从 '张三' 到 '联通公司' 的最短路径:", kg.find_shortest_path("张三", "联通公司"))
    print("从 '王五' 到 '中国' 的最短路径:", kg.find_shortest_path("王五", "中国"))

    # 示例 6: 获取某实体的上下文信息（多跳查询）
    print("\n=== 获取上下文信息 ===")
    print("大王卡的上下文信息（2 跳）:", kg.get_context("大王卡", depth=2))

    # 示例 7: 导出知识图谱为 JSON 格式
    print("\n=== 导出知识图谱为 JSON ===")
    print(kg.export_for_llm(format="json"))

    # 示例 8: 删除三元组
    print("\n=== 删除三元组 ===")
    kg.delete_triplet(entity1="张三", relation="办理", entity2="大王卡")
    print("张三的关系（删除后）:", kg.query_relationships("张三"))
