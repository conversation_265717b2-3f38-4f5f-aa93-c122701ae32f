from typing import List

from chatbi.memory import MemoryUnit
from chatbi.memory.memories.short_term_memory import ShortTermMemory
from chatbi.llms.base import BaseLLM


class ConversationSummaryMemory(ShortTermMemory):

    def __init__(self, llm: BaseLLM = None) -> None:
        super(ConversationSummaryMemory, self).__init__()
        self.model = llm
        if self.model is None:
            raise ValueError("LLM is required for ConversationSummaryMemory. Please provide a valid LLM instance.")

    def build_history(self, prompt_id: str, max_length: int = 2048) -> str:
        history = super().build_history(prompt_id=prompt_id, max_length=max_length)
        prompt = f"{history}，请将以上提供的历史对话内容进行一段简洁的总结:"
        summary = self.model.invoke(query=prompt)
        return summary


if __name__ == "__main__":
    from chatbi.llms.chatbi_llm import ChatBILLM

    llm = ChatBILLM(api_key='sk-68ac5f5ccf3540ba834deeeaecb48987',
                    base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
                    model_name="qwen-plus")

    memory_manager = ConversationSummaryMemory(llm=llm)
    memory_manager.add_memory("prompt1", {"input": "Hello", "output": "Hi"})
    memory_manager.add_memory("prompt1", {"input": "How are you?", "output": "I'm fine"})
    memory_manager.add_memory("prompt1", {"input": "What’s your name?", "output": "I'm ChatGPT"})
    memory_manager.add_memory("prompt1", {"input": "Tell me a joke", "output": "Why did the chicken cross the road?"})

    print("\nPrompt1 Memories:")
    for mem in memory_manager.get_memories("prompt1"):
        print(mem)

    print("\nTop Memories for Prompt1:")
    for mem in memory_manager.get_top_memories("prompt1"):
        print(mem)

    # 调用 build_history 方法
    print("\nGenerated History for Prompt1:")
    memories = memory_manager.get_memories("prompt1")
    history = memory_manager.build_history("prompt1", max_length=200)
    print(history)

