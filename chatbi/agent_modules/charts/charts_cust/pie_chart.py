from chatbi.agent_modules.charts.charts_cust.base_chart import BaseChart


class PieChart(BaseChart):
    def validate_config(self):
        # 饼图只允许一个数据列
        if not self.data_columns or len(self.data_columns) != 1:
            raise ValueError("饼图只能有一个 'data' 列。")

        # label 列只能为空或有一个
        if len(self.label_columns) > 1:
            raise ValueError("饼图最多只能有一个 'label' 列。")

    def generate_options(self):
        self.options = {
            'title': {
                'text': self.title,
                'subtext': self.subtitle,
                'left': 'center'
            },
            'tooltip': {
                'trigger': 'item',
                'formatter': '{a} <br/>{b}: {c} ({d}%)'
            },
            'legend': {'left': 'left'},
            'series': [],
        }
        if self.color:
            self.options['color'] = self.color

        pie_data = []
        if self.label_columns:
            labels = self.data[self.label_columns[0]].astype(str).tolist()
            for col in self.data_columns:
                values = self.data[col].tolist()
                pie_data.extend([{'name': label, 'value': value} for label, value in zip(labels, values)])
            self.options['legend']['data'] = labels
        else:
            for col in self.data_columns:
                total = self.data[col].sum()
                pie_data.append({'name': col, 'value': total})
            self.options['legend']['data'] = [col for col in self.data_columns]

        series_data = {
            'name': self.title,
            'type': 'pie',
            'radius': '55%',
            'center': ['50%', '60%'],
            'data': pie_data,
            'emphasis': {
                'itemStyle': {
                    'shadowBlur': 10,
                    'shadowOffsetX': 0,
                    'shadowColor': 'rgba(0, 0, 0, 0.5)'
                }
            }
        }
        self.options['series'].append(series_data)

