from chatbi.agent_modules.charts.charts_cust.base_chart import BaseChart
from chatbi.agent_modules.charts.tools.digital_tools import get_unit
import pandas as pd


class PieChartPro(BaseChart):
    def validate_config(self):
        # 饼图只允许一个数据列
        if not self.data_columns or len(self.data_columns) != 1:
            raise ValueError("饼图只能有一个 'data' 列。")

        # label 列只能为空或有一个
        if len(self.label_columns) > 1:
            raise ValueError("饼图最多只能有一个 'label' 列。")

    def generate_options(self):
        data = self.data
        for col in self.data_columns:
            data[col] = pd.to_numeric(data[col], errors='coerce')
        self.data = data

        y_data = self.data[self.data_columns].values.flatten()
        min_indicator_value = y_data.min()
        max_indicator_value = y_data.max()
        unit_base, value_unit = get_unit(max_indicator_value)

        self.options = {
            'title': {
                'text': self.title,
                # 'subtext': self.subtitle,
                'left': 'center',
                'y': '3%'
            },
            'tooltip': {
                'trigger': 'item',
                # 'formatter': '{a} <br/>{b}: {c} ({d}%)'
                'formatter': '{b}: {c} ({d}%)'
                # 'formatter': f"function(params) {{var seriesName = params.seriesName; var name = params.name; var valueInMillion = (params.value / {unit_base}).toFixed(2); return name + ' : ' + valueInMillion + '{value_unit} (' + params.percent.toFixed(2) + '%)'; }}"
            },
            'legend': {'type': 'scroll', 'orient': 'vertical', 'left': 'left', 'y': '10%'},
            'series': [],
        }
        # if self.color:
        #     self.options['color'] = self.color

        pie_data = []
        if self.label_columns:
            labels = self.data[self.label_columns[0]].astype(str).tolist()
            for col in self.data_columns:
                values = self.data[col].tolist()
                pie_data.extend([{'name': label, 'value': value} for label, value in zip(labels, values)])
            # self.options['legend']['data'] = labels # 可省略
        else:
            for col in self.data_columns:
                total = self.data[col].sum()
                pie_data.append({'name': col, 'value': total})
            # self.options['legend']['data'] = [col for col in self.data_columns] # # 可省略

        series_data = {
            'name': '指标：值 (占比)',
            'type': 'pie',
            'radius': '55%',
            # 'center': ['50%', '50%'],
            'data': pie_data,
            'emphasis': {
                'itemStyle': {
                    'shadowBlur': 10,
                    'shadowOffsetX': 0,
                    'shadowColor': 'rgba(0, 0, 0, 0.5)'
                }
            }
        }
        self.options['series'].append(series_data)

