from .base_chart import BaseChart


class RadarChart(BaseChart):
    def validate_config(self):
        if not self.data_columns:
            raise ValueError("配置中的 'data' 字段不能为空。")
        if not self.label_columns or len(self.label_columns) != 1:
            raise ValueError("雷达图需要且只能有一个 'label' 列。")

    def generate_options(self):
        labels = self.data[self.label_columns[0]].astype(str).tolist()
        max_value = self.data[self.data_columns].max().max()
        indicators = [{'name': label, 'max': max_value} for label in labels]

        self.options = {
            'title': {
                'text': self.title,
                'subtext': self.subtitle,
                'left': 'center'
            },
            'tooltip': {'trigger': 'item'},
            'legend': {'data': self.data_columns, 'left': 'left'},
            'radar': {'indicator': indicators},
            'series': [],
        }
        if self.color:
            self.options['color'] = self.color

        for col in self.data_columns:
            series_data = {
                'name': col,
                'type': 'radar',
                'data': [{
                    'value': self.data[col].tolist(),
                    'name': col
                }]
            }
            self.options['series'].append(series_data)
