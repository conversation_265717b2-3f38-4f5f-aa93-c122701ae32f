from chatbi.agent_modules.charts.charts_cust.base_chart import BaseChart
import numpy as np


class BarChart(BaseChart):
    def validate_config(self):
        # 验证配置
        if not self.data_columns:
            raise ValueError("配置中的 'data' 字段不能为空。")
        if not self.label_columns or len(self.label_columns) != 1:
            raise ValueError("柱状图需要且只能有一个 'label' 列。")

        self.validate_data_types()

    def validate_data_types(self):
        """验证数据类型，确保 value 是数值类型"""
        for col in self.data_columns:
            if not np.issubdtype(self.data[col].dtype, np.number):
                raise ValueError(f"数据列 {col} 包含非数值数据，无法生成柱状图。请检查输入数据。")

    def generate_options(self):
        # 配置项的生成
        self.options = {
            'title': {
                'text': self.title,
                'subtext': self.subtitle,
                'left': 'center'
            },
            'tooltip': {'trigger': 'axis'},
            'legend': {'data': self.data_columns, 'left': 'left'},
            'xAxis': {
                'type': 'category',
                'data': self.data[self.label_columns[0]].astype(str).tolist()
            },
            'series': [],
        }

        y_axis_config = {
            'type': 'value',
            'scale': True,
            'axisLabel': {
                'formatter': '{value}'
            },
            'axisPointer': {
                'snap': True
            }
        }

        adjust_axis = self.config.get('adjust_axis', False)
        y_min = self.config.get('y_min')
        y_max = self.config.get('y_max')

        if adjust_axis:
            y_min = self.calculate_axis_min() if y_min is None else y_min
            y_max = self.calculate_axis_max() if y_max is None else y_max
            y_axis_config['min'] = y_min
            y_axis_config['max'] = y_max

        self.options['yAxis'] = y_axis_config

        if self.color:
            self.options['color'] = self.color

        for idx, col in enumerate(self.data_columns):
            series_data = {
                'name': col,
                'type': 'bar',
                'data': self.data[col].tolist(),
            }
            self.options['series'].append(series_data)

    def calculate_axis_min(self):
        y_data = self.data[self.data_columns].values.flatten()
        y_min = y_data.min()
        y_max = y_data.max()
        margin = (y_max - y_min) * 0.1
        return y_min - margin

    def calculate_axis_max(self):
        y_data = self.data[self.data_columns].values.flatten()
        y_min = y_data.min()
        y_max = y_data.max()
        margin = (y_max - y_min) * 0.1
        return y_max + margin
