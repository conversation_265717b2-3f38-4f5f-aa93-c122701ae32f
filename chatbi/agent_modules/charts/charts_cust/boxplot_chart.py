from chatbi.agent_modules.charts.charts_cust.base_chart import BaseChart
import numpy as np


class BoxplotChart(BaseChart):
    def validate_config(self):
        if not self.data_columns or len(self.data_columns) != 1:
            raise ValueError("箱线图需要且只能有一个 'data' 列。")
        if self.label_columns:
            raise ValueError("箱线图不需要 'label' 列。")

    def generate_options(self):
        data_values = self.data[self.data_columns[0]].dropna().values
        box_data = self.calculate_boxplot_data(data_values)

        self.options = {
            'title': {
                'text': self.title,
                'subtext': self.subtitle,
                'left': 'center'
            },
            'tooltip': {
                'trigger': 'item',
                'formatter': '{b}: {c}'
            },
            'yAxis': {
                'type': 'category',
                'data': [self.data_columns[0]]
            },
            'xAxis': {
                'type': 'value'
            },
            'series': [{
                'name': self.data_columns[0],
                'type': 'boxplot',
                'data': [box_data]
            }]
        }
        if self.color:
            self.options['color'] = self.color

    @staticmethod
    def calculate_boxplot_data(data):
        quartile1, median, quartile3 = np.percentile(data, [25, 50, 75])
        min_value = np.min(data)
        max_value = np.max(data)
        return [min_value, quartile1, median, quartile3, max_value]
