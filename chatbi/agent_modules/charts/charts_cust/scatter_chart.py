from .base_chart import BaseChart


class ScatterChart(BaseChart):
    def validate_config(self):
        if not self.data_columns or len(self.data_columns) != 2:
            raise ValueError("散点图需要且只能有两个 'data' 列。")
        if self.label_columns:
            raise ValueError("散点图不需要 'label' 列。")

    def generate_options(self):
        x_data = self.data[self.data_columns[0]].tolist()
        y_data = self.data[self.data_columns[1]].tolist()
        scatter_data = list(zip(x_data, y_data))

        self.options = {
            'title': {
                'text': self.title,
                'subtext': self.subtitle,
                'left': 'center'
            },
            'tooltip': {
                'trigger': 'item',
                'formatter': '({c})'
            },
            'xAxis': {
                'type': 'value',
                'scale': True,
            },
            'yAxis': {
                'type': 'value',
                'scale': True,
            },
            'series': [{
                'name': self.title,
                'type': 'scatter',
                'data': scatter_data
            }],
        }

        adjust_axis = self.config.get('adjust_axis', False)
        x_min = self.config.get('x_min')
        x_max = self.config.get('x_max')
        y_min = self.config.get('y_min')
        y_max = self.config.get('y_max')

        if adjust_axis:
            # 调整 x 轴范围
            x_axis_config = self.options['xAxis']
            x_min = self.calculate_axis_min(self.data_columns[0]) if x_min is None else x_min
            x_max = self.calculate_axis_max(self.data_columns[0]) if x_max is None else x_max
            x_axis_config['min'] = x_min
            x_axis_config['max'] = x_max

            # 调整 y 轴范围
            y_axis_config = self.options['yAxis']
            y_min = self.calculate_axis_min(self.data_columns[1]) if y_min is None else y_min
            y_max = self.calculate_axis_max(self.data_columns[1]) if y_max is None else y_max
            y_axis_config['min'] = y_min
            y_axis_config['max'] = y_max

        if self.color:
            self.options['color'] = self.color

    def calculate_axis_min(self, column):
        data = self.data[column].values
        data_min = data.min()
        data_max = data.max()
        margin = (data_max - data_min) * 0.1  # 添加 10% 的余量
        return data_min - margin

    def calculate_axis_max(self, column):
        data = self.data[column].values
        data_min = data.min()
        data_max = data.max()
        margin = (data_max - data_min) * 0.1  # 添加 10% 的余量
        return data_max + margin
