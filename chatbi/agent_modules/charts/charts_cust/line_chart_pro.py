from chatbi.agent_modules.charts.charts_cust.base_chart import BaseChart
from chatbi.agent_modules.charts.tools.digital_tools import get_unit
import pandas as pd


class LineChartPro(BaseChart):
    def validate_config(self):
        if not self.data_columns:
            raise ValueError("配置中的 'data' 字段不能为空。")
        if not self.label_columns or len(self.label_columns) != 1:
            raise ValueError("折线图需要且只能有一个 'label' 列。")

    def generate_options(self):
        data = self.data
        for col in self.data_columns:
            data[col] = pd.to_numeric(data[col], errors='coerce')
        self.data = data
        y_data = self.data[self.data_columns].values.flatten()
        min_indicator_value = y_data.min()
        max_indicator_value = y_data.max()
        unit_base, value_unit = get_unit(max_indicator_value)
        print(max_indicator_value, unit_base, value_unit)

        # zero_indicator_value = int((3 * min_indicator_value - max_indicator_value) / (2 * unit_base))
        zero_indicator_value = round(min_indicator_value / unit_base, 2)
        if min_indicator_value > 0:
            zero_indicator_value = max(0, zero_indicator_value)

        x_len = len(self.data[self.data_columns[0]].unique().tolist())
        if x_len > 15:
            data_zoom_start = 100 - int(15 * 100 / x_len)
        else:
            data_zoom_start = 0
        data_zoom_end = 100
        self.options = {
            'title': {
                'text': self.title,
                # 'subtext': self.subtitle,
                'x': 'center',
                'y': 'top'
            },
            'tooltip': {
                'trigger': 'axis',
                "axisPointer": {
                    "type": "cross",
                    "crossStyle": {
                        "color": "#000"
                    }
                }
            },
            'legend': {'data': self.data_columns, "left": "center", "y": "10%"},
             "grid": [
                {
                    "top": "20%",
                    "left": "5%",
                    "right": "5%",
                    "height": "60%",
                    "containLabel": True
                }
            ],
            "toolbox": {},
            "xAxis": [
                {
                    "type": "category",
                    "boundaryGap": True,
                    "gridIndex": 0,
                    "data": self.data[self.label_columns[0]].astype(str).tolist()
                }
            ],
            "yAxis": [
                {
                    "type": "value",
                    "gridIndex": 0,
                    "min": zero_indicator_value,
                    "axisLabel": {
                        "formatter": "{value}" + f"{value_unit}"
                    }
                }
            ],
            "dataZoom": [
                {
                    "show": True,
                    "realtime": True,
                    "start": data_zoom_start,
                    "end": data_zoom_end,
                    "xAxisIndex": [
                        0
                    ],
                    "top": "85%",
                    "height": "2%"
                },
            ],
            "series": [
            ]
        }

        # if self.color:
        #     self.options['color'] = self.color

        for idx, col in enumerate(self.data_columns):
            series_data = {
                    "name": col,
                    "type": "line",
                    "barWidth": "auto",
                    "xAxisIndex": 0,
                    "yAxisIndex": 0,
                    "smooth": True,
                    # "color": "#3D8AD3",
                    # "color": "#63b2ee", # color 不设置 echarts 会自动适配
                    "itemStyle": {
                        "opacity": 0.7
                    },
                    "label": {
                        "show": True,
                        "position": "top"
                    },
                    "data": [round(value / unit_base, 2) for value in self.data[col].to_list()],
                }
            self.options['series'].append(series_data)

    def calculate_axis_min(self):
        y_data = self.data[self.data_columns].values.flatten()
        y_min = y_data.min()
        y_max = y_data.max()
        margin = (y_max - y_min) * 0.1
        return y_min - margin

    def calculate_axis_max(self):
        y_data = self.data[self.data_columns].values.flatten()
        y_min = y_data.min()
        y_max = y_data.max()
        margin = (y_max - y_min) * 0.1
        return y_max + margin
