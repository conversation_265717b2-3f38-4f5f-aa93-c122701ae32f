from typing import List, Dict, Any
import pandas as pd
import json
from abc import ABC, abstractmethod


class BaseChart:
    def __init__(self, config: Dict[str, Any], data: pd.DataFrame):
        self.config = config
        self.data = data
        self.options = {}
        self.chart_type = config.get('type', '')
        self.data_columns = config.get('data', [])
        self.label_columns = config.get('label', [])
        self.title = config.get('title', '数据图表')
        self.subtitle = config.get('subtitle', '')
        self.color = config.get('color', None)

    @abstractmethod
    def validate_config(self):
        """验证配置参数，需在子类中实现"""
        pass

    def validate_data_columns(self, columns: List[str]):
        """验证数据中是否包含指定的列"""
        for col in columns:
            if col not in self.data.columns:
                raise ValueError(f"数据中不包含列：{col}")

    @abstractmethod
    def generate_options(self):
        """生成 ECharts 配置项，需在子类中实现"""
        pass

    def get_options(self) -> Dict[str, Any]:
        return self.options

    def to_json(self) -> str:
        return json.dumps(self.options, indent=2, ensure_ascii=False)
