from chatbi.agent_modules.charts.charts_cust.base_chart import BaseChart


class FunnelChart(BaseChart):
    def validate_config(self):
        if not self.data_columns or len(self.data_columns) != 1:
            raise ValueError("漏斗图需要且只能有一个 'data' 列。")
        if not self.label_columns or len(self.label_columns) != 1:
            raise ValueError("漏斗图需要且只能有一个 'label' 列。")

    def generate_options(self):
        labels = self.data[self.label_columns[0]].astype(str).tolist()
        values = self.data[self.data_columns[0]].tolist()
        funnel_data = [{'name': label, 'value': value} for label, value in zip(labels, values)]

        self.options = {
            'title': {
                'text': self.title,
                'subtext': self.subtitle,
                'left': 'center'
            },
            'tooltip': {
                'trigger': 'item',
                'formatter': '{b}: {c}'
            },
            'legend': {
                'data': labels,
                'left': 'left'
            },
            'series': [{
                'name': self.title,
                'type': 'funnel',
                'data': funnel_data
            }]
        }
        if self.color:
            self.options['color'] = self.color
