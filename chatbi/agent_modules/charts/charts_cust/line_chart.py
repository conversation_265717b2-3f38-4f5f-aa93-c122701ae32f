from .base_chart import BaseChart


class LineChart(BaseChart):
    def validate_config(self):
        if not self.data_columns:
            raise ValueError("配置中的 'data' 字段不能为空。")
        if not self.label_columns or len(self.label_columns) != 1:
            raise ValueError("折线图需要且只能有一个 'label' 列。")

    def generate_options(self):
        self.options = {
            'title': {
                'text': self.title,
                'subtext': self.subtitle,
                'left': 'center'
            },
            'tooltip': {'trigger': 'axis'},
            'legend': {'data': self.data_columns, 'left': 'left'},
            'xAxis': {
                'type': 'category',
                'data': self.data[self.label_columns[0]].astype(str).tolist()
            },
            'yAxis': {
                'type': 'value',
                # 动态调整 y 轴范围
                'min': self.calculate_axis_min(),
                'max': self.calculate_axis_max(),
                'scale': True
            },
            'series': [],
        }

        if self.color:
            self.options['color'] = self.color

        for idx, col in enumerate(self.data_columns):
            series_data = {
                'name': col,
                'type': 'line',
                'data': self.data[col].tolist(),
                'smooth': True
            }
            self.options['series'].append(series_data)

    def calculate_axis_min(self):
        y_data = self.data[self.data_columns].values.flatten()
        y_min = y_data.min()
        y_max = y_data.max()
        margin = (y_max - y_min) * 0.1
        return y_min - margin

    def calculate_axis_max(self):
        y_data = self.data[self.data_columns].values.flatten()
        y_min = y_data.min()
        y_max = y_data.max()
        margin = (y_max - y_min) * 0.1
        return y_max + margin
