option_multi_pie = {
  title: {
    text: '江苏省各项指标值分布',
    subtext: '',
    left: 'center',
    y: '3%'
  },
  tooltip: {
    trigger: 'item',
    formatter: function (params) {
      // 获取系列名称
      var seriesName = params.seriesName;
      // 获取项名称
      var name = params.name;
      // 将数值转换成“xxx万”格式
      var valueInMillion = (params.value / 10000).toFixed(2);
      // 返回格式化的字符串
      return (
        seriesName +
        '<br>' +
        name +
        ' : ' +
        valueInMillion +
        '万 (' +
        params.percent.toFixed(2) +
        '%)'
      );
    }
  },
  legend: [
    {
      type: 'scroll',
      orient: 'vertical',
      left: 'left',
      y: '10%',
      data: [
        '离网用户数',
        '在网用户数',
        '移动用户数',
        '组网用户数',
        '家宽客户数',
        '流量用户数',
        '爱家用户数'
      ]
    }
  ],
  series: [
    {
      name: '指标：值 (占比)',
      type: 'pie',
      radius: '55%',
      center: ['50%', '50%'],
      data: [
        { value: 5470000, name: '离网用户数' },
        { value: 5000000, name: '在网用户数' },
        { value: 4000000, name: '移动用户数' },
        { value: 8000000, name: '组网用户数' },
        { value: 4500000, name: '家宽客户数' },
        { value: 4800000, name: '流量用户数' },
        { value: 5500000, name: '爱家用户数' }
      ],
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      }
    }
  ]
};


option_single_pie = {
  title: { text: '各公司研发投入比例', left: 'center', y: '3%' },
  tooltip: {
    trigger: 'item',
    formatter: function (params) {
      var seriesName = params.seriesName;
      var name = params.name;
      var valueInMillion = (params.value / 100000000.0).toFixed(2);
      return (
        name +
        ' : ' +
        valueInMillion +
        '亿 (' +
        params.percent.toFixed(2) +
        '%)'
      );
    }
  },
  legend: {
    type: 'scroll',
    orient: 'vertical',
    left: 'left',
    y: '10%'
  },
  series: {
    name: '指标：值 (占比)',
    type: 'pie',
    radius: '55%',
    data: [
      { name: '安徽', value: 810406096.22 },
      { name: '北京', value: 870220013.95 },
      { name: '福建', value: 814534371.16 },
      { name: '甘肃', value: 248949203.76 },
      { name: '广东', value: 2902232304.09 }
    ],
    emphasis: {
      itemStyle: {
        shadowBlur: 10,
        shadowOffsetX: 0,
        shadowColor: 'rgba(0, 0, 0, 0.5)'
      }
    }
  }
};
