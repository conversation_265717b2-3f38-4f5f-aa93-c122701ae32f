simple_opt = """{{
    "title": {{
      'text': '{title}',
      'x': '{title_x}',
      'y': '{title_y}'
    }},
    "tooltip": {{
        "trigger": "axis",
        "axisPointer": {{
            "type": "cross",
            "crossStyle": {{
                "color": "#000"
            }}
        }}
    }},
    "legend": {{
        "data": [
            '{indicator}'
        ],
        "left": "center",
        "y": "25%"
    }},
    "grid": [
        {{
            "top": "40%",
            "left": "5%",
            "right": "5%",
            "height": "30%",
            "containLabel": true
        }}
    ],
    "toolbox": {{}},
    "xAxis": [
        {{
            "type": "category",
            "boundaryGap": true,
            "gridIndex": 0,
            'data': {x_label},
        }}
    ],
    "yAxis": [
        {{ 
        'type': 'value', 'gridIndex': 0,
        'min': {zero_indicator_value},
        'axisLabel': {{
          'formatter': '{{value}}{value_unit}'
        }},
      }}
    ],
    "dataZoom": [
        {{
            "show": true,
            "realtime": true,
            "start": {data_zoom_start},
            "end": {data_zoom_end},
            "xAxisIndex": [
                0,
            ],
            "top": "75%",
            "height": "3%"
        }},
        {{
            "type": "inside",
            "realtime": true,
            "xAxisIndex": [
                0,
            ],
            "filterMode": "none"
        }}
    ],
    "series": [
        {{
            "name": '{indicator}',
            "type": "{chart_type}",
            "barWidth": "auto",
            "xAxisIndex": 0,
            "yAxisIndex": 0,
            "color": '{bar_color}',
            "itemStyle": {{
                "opacity": 0.7
            }},
            "label": {{
                "show": true,
                "position": "top"
            }},
            "data": {indicator_value}
        }}
    ]
}}"""

pie_opt = """{{
  title: {{
    text: '{title}',
    subtext: '{sub_title}',
    left: 'center',
    y: '3%'
  }},
  tooltip: {{
    trigger: 'item',
    formatter: '{{a}} <br/>{{b}} : {{c}} ({{d}}%)'
  }},
  legend: {{
    type: 'scroll',
    orient: 'vertical',
    left: 'left',
    y: '10%'
  }},
  series: [
    {{
      name: '值(占比)',
      type: 'pie',
      radius: '50%',
      center: ['50%', '50%'], 
      data: [
        {pie_data}
      ],
      emphasis: {{
        itemStyle: {{
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }}
      }}
    }},
  ]
}}"""

pie_opt_data = """{{ value: {value}, name: '{name}' }}, \n"""

si_sl_mt_no_rate_opt = """{{
    "title": {{
      'text': '{title}',
      'x': '{title_x}',
      'y': '{title_y}'
    }},
    "tooltip": {{
        "trigger": "axis",
        "axisPointer": {{
            "type": "cross",
            "crossStyle": {{
                "color": "#000"
            }}
        }}
    }},
    "legend": {{
        "data": [
            '{indicator}'
        ],
        "left": "center",
        "y": "25%"
    }},
    "grid": [
        {{
            "top": "40%",
            "left": "5%",
            "right": "5%",
            "height": "30%",
            "containLabel": true
        }}
    ],
    "toolbox": {{}},
    "xAxis": [
        {{
            "type": "category",
            "boundaryGap": true,
            "gridIndex": 0,
            'data': {x_label},
        }}
    ],
    "yAxis": [
        {{ 
        'type': 'value', 'gridIndex': 0,
        'min': {zero_indicator_value},
        'axisLabel': {{
          'formatter': '{{value}}{value_unit}'
        }},
      }}
    ],
    "dataZoom": [
        {{
            "show": true,
            "realtime": true,
            "start": {data_zoom_start},
            "end": {data_zoom_end},
            "xAxisIndex": [
                0,
            ],
            "top": "75%",
            "height": "3%"
        }},
        {{
            "type": "inside",
            "realtime": true,
            "xAxisIndex": [
                0,
            ],
            "filterMode": "none"
        }}
    ],
    "series": [
        {{
            "name": '{indicator}',
            "type": "bar",
            "barWidth": "auto",
            "xAxisIndex": 0,
            "yAxisIndex": 0,
            "color": '{bar_color}',
            "itemStyle": {{
                "opacity": 0.7
            }},
            "label": {{
                "show": true,
                "position": "top"
            }},
            "data": {indicator_value}
        }}
    ]
}}"""

si_ml_st_no_rate_opt = """{{
    "title": {{
      'text': '{title}',
      'x': '{title_x}',
      'y': '{title_y}'
    }},
    "tooltip": {{
        "trigger": "axis",
        "axisPointer": {{
            "type": "cross",
            "crossStyle": {{
                "color": "#000"
            }}
        }}
    }},
    "legend": {{
        "data": [
            '{indicator}'
        ],
        "left": "center",
        "y": "25%"
    }},
    "grid": [

        {{
            "top": "40%",
            "left": "5%",
            "right": "5%",
            "height": "30%",
            "containLabel": true
        }}
    ],
    "toolbox": {{}},
    "xAxis": [
        {{
            "type": "category",
            "boundaryGap": true,
            "gridIndex": 0,
            'data': {x_label},
        }}
    ],
    "yAxis": [
        {{ 
        'type': 'value', 'gridIndex': 0,
        'min': {zero_indicator_value},
        'axisLabel': {{
          'formatter': '{{value}}{value_unit}'
        }},
      }}
    ],
    "dataZoom": [
        {{
            "show": true,
            "realtime": true,
            "start": {data_zoom_start},
            "end": {data_zoom_end},
            "xAxisIndex": [
                0,
            ],
            "top": "75%",
            "height": "3%"
        }},
        {{
            "type": "inside",
            "realtime": true,
            "xAxisIndex": [
                0,
            ],
            "filterMode": "none"
        }}
    ],
    "series": [
        {{
            "name": '{indicator}',
            "type": "bar",
            "barWidth": "auto",
            "xAxisIndex": 0,
            "yAxisIndex": 0,
            "color": '{bar_color}',
            "itemStyle": {{
                "opacity": 0.7
            }},
            "label": {{
                "show": true,
                "position": "top"
            }},
            "data": {indicator_value}
        }}
    ]
}}"""