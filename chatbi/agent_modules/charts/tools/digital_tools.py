def format_number(num):
    if isinstance(num, (int, float)):
        abs_num = abs(num)  # absolute value of num
        if abs_num >= 1e12:
            return "{:.2f}万亿".format(num / 1e12)
        elif abs_num >= 1e8:
            return "{:.2f}亿".format(num / 1e8)
        elif abs_num >= 1e4:
            return "{:.2f}万".format(num / 1e4)
        else:
            if isinstance(num, int):
                return f"{num}"
            else:
                return f"{round(num, 2)}"
    else:
        raise ValueError("输入类型错误，要求输入是int或float类型。")


def safe_format_number(value_str:str):
    try:
        num = float(value_str)
        return format_number(num)
    except:
        print(f"{value_str} can not be format")
        return value_str


def split_num(num):
    if isinstance(num, (int, float)):
        abs_num = abs(num)  # absolute value of num
        if abs_num >= 1e12:
            return round(num / 1e12, 2), "万亿"
        elif abs_num >= 1e8:
            return round(num / 1e8, 2), "亿"
        elif abs_num >= 1e4:
            return round(num / 1e4, 2), "万"
        else:
            if isinstance(num, int):
                return num, ""
            else:
                return round(num, 2), ""
    else:
        raise ValueError("输入类型错误，要求输入是int或float类型。")


def get_unit(num):
    # print(num, type(num))
    try:
        num = float(num)
        # isinstance(num, (int, float)):
        abs_num = abs(num)  # absolute value of num
        if abs_num >= 1e12:
            return 1e12, "万亿"
        elif abs_num >= 1e8:
            return 1e8, "亿"
        elif abs_num >= 1e4:
            return 1e4, "万"
        else:
            return 1.0, ""
    except Exception as e:
        print(num, e)
        raise ValueError("输入类型错误，要求输入是int或float类型。")


def safe_float(value_str:str):
    try:
        return float(value_str)
    except:
        return value_str


if __name__ == "__main__":
    # 使用示例
    print(format_number(550000000000))  # 
    print(format_number(550000000))  # 5.50亿
    print(format_number(87320))      # 8.73万
    print(format_number(100)) 