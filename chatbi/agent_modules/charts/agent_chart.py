"""
画图Agent

支持对图表修改，创建图表
"""

from chatbi.agent_modules.charts.chart_generator import ChartGenerator
import pandas as pd
from chatbi.agent.base import BaseAgent


class ChartAgent(BaseAgent):

    def generate_chart_options(self,
                               query: str,
                               data: pd.DataFrame) -> str:
        """
        :param query: 用户的问题
        :param data: 数据 Pandas
        :return:
        """

        # 图表生成
        chart_agent = ChartGenerator(llm=self.config.llm)
        chart_agent.generate_chart(query=query, data=data)
        chart_options = chart_agent.get_options()

        self.stream.stream_message(content=chart_options,
                                   content_type='chart',
                                   ignore_tag=True)

        # 把图加入记忆
        if self.memory:
            self.memory.add_memory(prompt_id='chart', content={'chart': chart_options})

        return chart_options

    def modify_chart_options(self, pre_charts, query) -> str:
        """
        修改图表配置信息
        :return:
        """
        chart_agent = ChartGenerator(llm=self.config.llm)
        new_charts = chart_agent.modify_charts(query=query, echarts_option=pre_charts)
        return new_charts



