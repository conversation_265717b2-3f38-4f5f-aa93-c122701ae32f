from chatbi.prompts.base import BasePrompt


class GetChartConfig(BasePrompt):
    template_path = 'chatbi/agent_modules/charts/prompts/templates/get_chart_config.tmpl'
    template_desc = '生成图表配置信息'


class GetManipulateOptions(BasePrompt):
    template_path = 'chatbi/agent_modules/charts/prompts/templates/get_manipulate_options.tmpl'
    template_desc = '自然语言操控图表配置'


class GetVisualizeIntent(BasePrompt):
    template_desc = '获取用户意图（修图 or 不修图）'
    template_path = 'chatbi/agent_modules/charts/prompts/templates/get_intent.tmpl'

