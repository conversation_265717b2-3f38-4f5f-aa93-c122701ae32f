import pandas as pd
from typing import Dict, Any, Union
from chatbi.agent_modules.charts.engine import ChartEngine
from chatbi.agent_modules.charts.prompts.charts_prompts import *
from chatbi.output_parsers.json_output_parser import JsonOutputParser
import re, json
from chatbi.llms.base import BaseLLM


def str2dict(options):
    options = options.replace('true', 'True').replace('false', 'False')
    regex = r'(function\(([^)]*)\)\s*\{([^}]*)\})'  # 匹配function参数和大括号内的内容
    # 使用正则表达式查找匹配项
    matches = re.findall(regex, options)
    # 如果找到了匹配项，则构建函数字符串
    function_strings = []
    for match in matches:
        params = match[1]
        body = match[2].strip()  # 去除换行符并去掉两端空白
        function_strings.append(f'function({params}) {{{body}}}')
        options = options.replace(match[0], f'"{match[0]}"')
    options = eval(options)
    return options


def dict2str(options):
    options = str(options)
    regex = r'(function\(([^)]*)\)\s*\{([^}]*)\})'  # 匹配function参数和大括号内的内容
    # 使用正则表达式查找匹配项
    matches = re.findall(regex, options)
    # 如果找到了匹配项，则构建函数字符串
    function_strings = []
    for match in matches:
        params = match[1]
        body = match[2].strip()  # 去除换行符并去掉两端空白
        function_strings.append(f'function({params}) {{{body}}}')
        options = options.replace(f'"{match[0]}"', match[0])
    options = options.replace('True', 'true').replace('False', 'false')
    return options


def correct_code(code):
    code = code.strip()
    pm = re.compile(r'```[\w+-]*\n(.*?)```', re.DOTALL)  # 筛选被```包含的代码部分
    matches = pm.findall(code)
    if matches:
        code = matches[-1]
    return code


class ChartGenerator:
    def __init__(self, llm: BaseLLM):
        if not llm:
            raise ValueError('llm is empty')
        self.llm = llm
        self.chart_config = None
        self.chart_engine = ChartEngine()
        self.options = None
        self.options_with_no_data = None
        self.conversation_history = []
        self.columns = None

    def modify_charts(self, query: str, echarts_option: str, data: pd.DataFrame = None):
        try:
            options = json.loads(echarts_option)
            code = self.get_manipulate_options(query, str(options))
            code = correct_code(code)
            exec(code)
            return json.dumps(options, ensure_ascii=False)
        except Exception as e:
            print("直接修改配置出错：", e)
            self.generate_chart(query=query, data=data)
            return self.get_options()

    def generate_chart(self, query: str, data: pd.DataFrame = None, sql: str = None):
        if data is None and self.options is not None:
            # 修改图表配置
            # column_names = list(data.columns)
            try:
                options = self.options
                code = self.get_manipulate_options(query, str(options))  # self.options_with_no_data
                code = correct_code(code)
                exec(code)
                self.options = options
                self.options_with_no_data = self._get_options_with_no_data(
                    json.dumps(self.options, ensure_ascii=False)  # , indent=4)
                )
            except Exception as e:
                print("配置修改出错", e)

        else:
            if len(data.columns) <= 1:
                self.options = None
                return

            max_attempts = 2
            attempts = 0

            column_names = list(data.columns)
            self.columns = column_names

            while attempts < max_attempts:
                attempts += 1
                # 调用大模型生成图表配置
                response = self.get_chart_config(query, column_names, sql)
                self.chart_config = response

                try:
                    self.chart_engine.load_config(config=self.chart_config, data=data)
                    self.options = self.get_options_dict()
                    break
                except Exception as e:
                    # 生成失败，反馈错误信息
                    error_message = f"生成图表配置时出错：{e}"
                    print(error_message)
                    query = self.update_query_with_error(query, error_message)
                    continue
            else:
                # raise ValueError("无法生成有效的图表配置，请检查输入。") # 不能 raise 错误否则服务会 500 http error
                self.options = None

    def get_chart_config(self, query: str, column_names, sql):

        chart_prompt = GetChartConfig()
        chart_prompt.add_llm(model=self.llm)

        if sql is None:
            chart_prompt.update_placeholder(column_names=column_names)
            chain = chart_prompt - JsonOutputParser()
        else:
            sql = f"<SQL查询>\n{sql}"
            chart_prompt.update_placeholder(column_names=column_names, sql=sql)
            chain = chart_prompt - JsonOutputParser()
        response = chain.run(query=query)
        return response

    def get_manipulate_options(self, query: str, options: str):
        mp = GetManipulateOptions()
        mp.add_llm(model=self.llm)
        mp.update_placeholder(options=options)
        res = mp.call(query=query)
        return res

    def update_query_with_error(self, query: str, error_message: str) -> str:
        # 将错误信息添加到查询中，反馈给大模型
        updated_query = f"{query}\n注意：{error_message}，请根据报错信息重新生成正确的图表配置。你只能从{self.columns}中选取数据列名"
        return updated_query

    def get_options(self) -> Union[Dict[str, Any], str]:
        if self.options is None:
            options = self.chart_engine.get_options()
            options = dict2str(options)
            self.options_with_no_data = self._get_options_with_no_data(options)
            return options
        else:
            return json.dumps(self.options, ensure_ascii=False)  # , indent=4)

    def get_options_dict(self) -> Dict[str, Any]:
        if self.options is None:
            options = self.chart_engine.get_options()
            return options
        else:
            return self.options

    @staticmethod
    def _get_options_with_no_data(option: str) -> Union[Dict, str]:
        # 用于删除 option 中的数据部分，仅保留主体部分
        # 以供填充 prompt 时使用较少的token
        def process_dict(obj):
            if isinstance(obj, dict):
                for key in list(obj.keys()):
                    value = obj[key]
                    if key == 'data':
                        obj[key] = []
                    else:
                        process_dict(value)
            elif isinstance(obj, list):
                for index, item in enumerate(obj):
                    if isinstance(item, (dict, list)):
                        obj[index] = process_dict(item)
            return obj

        option_wnd = str2dict(option)
        process_dict(option_wnd)
        return option_wnd

    def to_echarts(self, filename: str = 'chart.html', width: str = '800px', height: str = '600px'):
        """返回HTML字符串"""
        return self.chart_engine.to_html(filename=filename, width=width, height=height)

