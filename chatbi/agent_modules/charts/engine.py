from typing import Dict, Any
import pandas as pd
from typing import Optional

# from chatbi.charts.line_chart import <PERSON><PERSON>hart
from chatbi.agent_modules.charts.charts_cust.line_chart_pro import LineChartPro
from chatbi.agent_modules.charts.charts_cust.bar_chart_pro import Bar<PERSON>hartP<PERSON>
from chatbi.agent_modules.charts.charts_cust.pie_chart_pro import <PERSON><PERSON><PERSON><PERSON><PERSON>
# from chatbi.charts.bar_chart import <PERSON><PERSON><PERSON>
# from chatbi.charts.pie_chart import Pie<PERSON>hart
from chatbi.agent_modules.charts.charts_cust.scatter_chart import Scatter<PERSON><PERSON>
from chatbi.agent_modules.charts.charts_cust.radar_chart import Radar<PERSON>hart
from chatbi.agent_modules.charts.charts_cust.area_chart import Area<PERSON>hart
from chatbi.agent_modules.charts.charts_cust.boxplot_chart import <PERSON><PERSON><PERSON><PERSON><PERSON>
from chatbi.agent_modules.charts.charts_cust.funnel_chart import Funnel<PERSON>hart

# 图表类型映射,(目前仅支持8个，20240923)
CHART_TYPE_MAP = {
    'line': LineChartPro,  # LineChart,
    'bar': <PERSON><PERSON><PERSON><PERSON><PERSON>,  # Bar<PERSON>hart,
    'pie': <PERSON><PERSON>hart<PERSON><PERSON>,  # Pie<PERSON><PERSON>
    'scatter': <PERSON><PERSON><PERSON><PERSON><PERSON>,
    'radar': <PERSON><PERSON><PERSON>,
    'area': <PERSON><PERSON><PERSON>,
    'boxplot': <PERSON><PERSON><PERSON><PERSON><PERSON>,
    'funnel': FunnelChart,
}


class ChartEngine:
    def __init__(self):
        self.chart_instance = None
        self.config = {}
        self.data: Optional[pd.DataFrame] = None

    def load_config(self, config: Dict[str, Any], data: pd.DataFrame):
        self.config = config
        self.data = data
        chart_type = config.get('type', '').lower()
        if chart_type not in CHART_TYPE_MAP:
            raise ValueError(f"不支持的图表类型：{chart_type}. 请使用以下图表类型之一：{', '.join(CHART_TYPE_MAP.keys())}")
        chart_class = CHART_TYPE_MAP.get(chart_type)
        if not chart_class:
            raise ValueError(f"不支持的图表类型：{chart_type}")

        self.chart_instance = chart_class(config, data)
        self.chart_instance.validate_data_columns(config.get('data', []) + config.get('label', []))
        self.chart_instance.validate_config()
        self.chart_instance.generate_options()

    def transfer_type(self, target_type: str):
        if not self.chart_instance:
            raise ValueError("尚未加载任何图表配置。")
        target_type = target_type.lower()
        chart_class = CHART_TYPE_MAP.get(target_type)
        if not chart_class:
            raise ValueError(f"不支持的目标图表类型：{target_type}")

        # 创建图表实例
        self.config['type'] = target_type
        new_chart_instance = chart_class(self.config, self.data)
        try:
            new_chart_instance.validate_data_columns(self.config.get('data', []) + self.config.get('label', []))
            new_chart_instance.validate_config()
            new_chart_instance.generate_options()
            self.chart_instance = new_chart_instance
        except ValueError as e:
            raise ValueError(f"无法转换为图表类型 '{target_type}'：{e}")

    def get_options(self) -> Dict[str, Any]:
        """获取图表配置信息，并用可被JsonParser解析的格式输出！双引号"""
        if not self.chart_instance:
            raise ValueError("尚未加载任何图表配置。")
        return self.chart_instance.get_options()

    def to_json(self) -> str:
        if not self.chart_instance:
            raise ValueError("尚未加载任何图表配置。")
        return self.chart_instance.to_json()

    def to_html(self, filename: str = 'chart.html', width: str = '600px', height: str = '400px'):
        """
        生成 ECharts 的 HTML 文件。
        filename: 生成的 HTML 文件名。
        width: 图表的宽度，默认值为 '600px'。
        height: 图表的高度，默认值为 '400px'。
        """
        if not self.chart_instance:
            raise ValueError("尚未加载任何图表配置。")

        echarts_options = self.chart_instance.to_json()
        html_content = f"""
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>ECharts</title>
            <script src="https://cdn.jsdelivr.net/npm/echarts@5.3.2/dist/echarts.min.js"></script>
        </head>
        <body>
            <div id="chart-container" style="width: {width}; height: {height};"></div>
            <script>
                var chart = echarts.init(document.getElementById('chart-container'));
                var options = {echarts_options};
                chart.setOption(options);
            </script>
        </body>
        </html>
        """
        # with open(filename, 'w', encoding='utf-8') as file:
        #     file.write(html_content)
        #
        # print(f"HTML 文件已生成: {filename}")

        return html_content
