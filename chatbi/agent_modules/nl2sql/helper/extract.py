"""
抽取各类用md包裹的内容
"""

import re


def extract_sql(text: str) -> str:
    """
    从文本中提取 ```sql ... ``` 代码块中的 SQL 内容。
    :param text: 包含 SQL 代码块的字符串
    :return: 提取出的 SQL 字符串，如果没有找到则返回空字符串
    """
    # 定义正则表达式模式
    # ```sql 后匹配任意字符（非贪婪模式），直到遇到 ```
    pattern = r"```sql\s*(.+?)\s*```"

    # 使用 re.DOTALL 使 . 匹配换行符
    match = re.search(pattern, text, re.DOTALL)

    # 如果找到匹配，返回提取的内容，否则返回空字符串
    return match.group(1).strip() if match else ""


def extract_python(text: str) -> str:
    """
    从文本中提取 ```python ... ``` 代码块中的 SQL 内容。
    :param text: 包含 SQL 代码块的字符串
    :return: 提取出的 SQL 字符串，如果没有找到则返回空字符串
    """
    # 定义正则表达式模式
    # ```python 后匹配任意字符（非贪婪模式），直到遇到 ```
    pattern = r"```python\s*(.+?)\s*```"

    # 使用 re.DOTALL 使 . 匹配换行符
    match = re.search(pattern, text, re.DOTALL)

    # 如果找到匹配，返回提取的内容，否则返回空字符串
    return match.group(1).strip() if match else ""


def extract_json(text: str) -> str:
    """
    从文本中提取 ```json ... ``` 代码块中的 SQL 内容。
    :param text: 包含 json 代码块的字符串
    :return: 提取出的 SQL 字符串，如果没有找到则返回空字符串
    """
    # 定义正则表达式模式
    # ```json 后匹配任意字符（非贪婪模式），直到遇到 ```
    pattern = r"```json\s*(.+?)\s*```"

    # 使用 re.DOTALL 使 . 匹配换行符
    match = re.search(pattern, text, re.DOTALL)

    # 如果找到匹配，返回提取的内容，否则返回空字符串
    return match.group(1).strip() if match else ""


def extract_html(text: str) -> str:
    """
    从文本中提取 ```html ... ``` 代码块中的 html 内容。
    :param text: 包含 json 代码块的字符串
    :return: 提取出的 SQL 字符串，如果没有找到则返回空字符串
    """
    # 定义正则表达式模式
    # ```json 后匹配任意字符（非贪婪模式），直到遇到 ```
    pattern = r"```html\s*(.+?)\s*```"

    # 使用 re.DOTALL 使 . 匹配换行符
    match = re.search(pattern, text, re.DOTALL)

    # 如果找到匹配，返回提取的内容，否则返回空字符串
    return match.group(1).strip() if match else ""


if __name__ == "__main__":
    # 测试用例 1：标准 SQL 代码块
    text1 = """
    Here is some text.
    ```sql
    SELECT name, age FROM students WHERE age > 18
    ```
    """
    print(extract_sql(text1))
