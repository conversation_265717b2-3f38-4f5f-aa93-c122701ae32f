from typing import List
from chatbi.utils.printf import printf
from chatbi.data_source.base import DataSource
from chatbi.sqlforge import ConditionParser
from chatbi.sqlforge import LiteralReplace


def _is_digit(s):
    try:
        float(s)  # 试着转换为 float，可以处理整数和浮点数
        return True
    except ValueError:
        return False


def sql_corrector(sql: str,
                  data_source: DataSource,
                  white_list: List[str] = []):
    """
    用于矫正SQL中的枚举值
    Args:
        white_list: 白名单，不需要进行替换
        sql: SQL查询
        data_source: 数据源
    Returns: new_sql
    """
    if not data_source or not isinstance(data_source, DataSource):
        raise ValueError('data_source must be DataSource')

    if not sql:
        raise ValueError('sql cannot be empty')

    # 获取DataSource的所有表名、列名
    ds_table_names: list = data_source.get_table_names_en()
    ds_column_names: list = data_source.get_column_en_names()

    parser = ConditionParser()
    corrector = LiteralReplace()

    # 拆解SQL
    parser_conditions: List[dict] = parser.get_conditions(sql=sql)

    # 找到操作为大于, 小于的条件
    for condition in parser_conditions:
        if condition['operator'] not in ['>', '<', '<=', '>=']:
            continue

        values: list = condition['value']
        table_name = condition['table_name_origin']
        column_name = condition['column_name']

        if table_name not in ds_table_names and column_name not in ds_column_names:
            continue

        value = values[0]

        # 判断是否为数值, 如果不是，跳过
        if not _is_digit(value):
            continue

        # 进行修改
        sql = corrector.replace(sql=sql,
                                old_value=value,
                                new_value=float(value),
                                column=column_name,
                                table=table_name)

    # 找到操作为EQ, IN的条件
    for condition in parser_conditions:

        if condition['operator'] not in ['EQ', 'IN', '!=']:
            continue

        values: list = condition['value']
        table_name = condition['table_name_origin']
        column_name = condition['column_name']

        if column_name in white_list:
            # 如果列名在白名单里面，则跳过
            continue

        if table_name not in ds_table_names and column_name not in ds_column_names:
            continue

        for value in values:
            # 判断是否为数值, 如果是，跳过
            if _is_digit(value):
                continue

            ds_enums = data_source.get_column_enums(table_name=table_name, column_name=column_name)

            if value in ds_enums:
                # 如果value就在枚举值里面，则跳过。
                pass
            else:
                most_similar_value = data_source.match_enums(table_name=table_name,
                                                             column_name=column_name,
                                                             value=value)
                # 进行修改
                sql = corrector.replace(sql=sql,
                                        old_value=value,
                                        new_value=most_similar_value,
                                        column=column_name,
                                        table=table_name)

                printf(title='字面值已矫正', color='green', message=f"[{value}] -> [{most_similar_value}]")

    return sql


