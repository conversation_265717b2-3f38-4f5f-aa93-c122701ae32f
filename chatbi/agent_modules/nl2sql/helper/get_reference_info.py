"""
生成SQL中，从数据源中获取相关信息
"""

from chatbi.data_source.base import DataSource
from typing import List, Optional
from chatbi.rag.chunk import Chunk
from chatbi.utils.printf import printf


def get_reference_sql(datasource: DataSource,
                      query: str) -> (Optional[str], Optional[str]):
    """
    在生成SQL中添加查找的参考信息

    :param datasource: 数据源
    :param query: 问题
    :return:
    content: str 知识的标题
    sql: str  对应的SQL
    """
    if datasource.knowledge is None:
        return None, None

    extra_infos: List[Chunk] = datasource.knowledge.search_knowledge(query=query, top_k=1)

    if extra_infos:
        extra_info = extra_infos[0]
        extra_sql = extra_info.metadata.get('sql', '')
        if not extra_sql or extra_sql == '':
            return None, None
        printf(color='green', title='参考SQL已检索成功', message=f'{extra_sql}')
        return extra_info.content, extra_sql
    return None, None


def get_reference_infos(datasource: DataSource,
                        query: str) -> Optional[List[str]]:
    """
    在生成SQL中添加查找的额外信息
    """
    if datasource.knowledge is None:
        return None

    extra_infos: List = datasource.knowledge.search_knowledge(query=query, top_k=5)

    extra_explains = []

    if extra_infos:
        for extra_info in extra_infos:
            explain = extra_info.metadata.get('explain', '')
            if not explain or explain == '':
                continue
            printf(color='green', title='参考额外信息已检索成功', message=f'{explain}')
            extra_explains.append(explain)

    return extra_explains
