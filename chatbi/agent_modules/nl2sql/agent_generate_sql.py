"""
生成SQL的Agent

最简单的生成SQL，没有思维链，直接给数据，出SQL
"""
from chatbi.agent.base import BaseAgent
import pandas as pd
import os
import uuid
from chatbi.data_source.base import DataSource

from chatbi.agent_modules.nl2sql.helper.today import get_today
from chatbi.agent_modules.nl2sql.helper.extract import extract_sql
from chatbi.agent_modules.nl2sql.helper.sql_corrector import sql_corrector
from chatbi.agent_modules.nl2sql.helper.get_reference_info import get_reference_sql, get_reference_infos
from chatbi.sqlforge.sql_transform import SQLOptimizer
from typing import Optional, Any, List


class BasicSQLAgent(BaseAgent):
    """
    基础版的NL2SQL
    """

    def generate_sql(self,
                     query: str,
                     datasource: DataSource) -> str:
        """
        仅生成SQL，但是需要加上验证
        """
        prompt_path = 'chatbi/agent_modules/nl2sql/prompts/gen_sql_basic.tmpl'

        # 生成SQL的提示器
        sql_prompter = self.create_prompt(template_path=prompt_path,
                                          template_desc='生成SQL')

        # 构建历史对话信息
        history = ""

        if self.memory:
            history: str = self.memory.build_history(prompt_id='sql',
                                                     max_round=10)

        # 准备数据信息
        tables_info: str = datasource.adaptive_schema(query=query)

        # 获取今天的日期
        date_time = get_today()

        # 获取参照SQL与参考信息
        content: Optional[str]
        ref_sql: Optional[str]
        knowledge: Optional[List[str]]

        content, ref_sql = get_reference_sql(datasource=datasource, query=query)
        knowledge: Optional[List[str]] = get_reference_infos(datasource=datasource, query=query)

        reference = ""

        if content or knowledge:
            content = content if content is not None else ""
            ref_sql = ref_sql if ref_sql is not None else ""
            knowledge = knowledge if knowledge is not None else ""
            reference = f"{content}\n{ref_sql}\n{knowledge}\n"

        # 填充占位符
        sql_prompter.update_placeholder(history=history,
                                        tables_info=tables_info,
                                        date_time=date_time,
                                        reference=reference)

        sql_resp_gen = sql_prompter.call(query=query,
                                         is_stream=True,
                                         return_generator=True)

        self.stream.stream_message(content='\n')
        sql_resp: str = self.stream.stream_to_response(generator=sql_resp_gen)
        self.stream.stream_message(content='\n')

        # 抽取出SQL代码块
        sql: str = extract_sql(sql_resp)

        # 对SQL进行修正和匹配
        try:
            sql: str = sql_corrector(sql=sql, data_source=datasource)
        except Exception as e:
            print(e)
            return sql

        MAX_ATTEMPTS = 3
        for attempt in range(MAX_ATTEMPTS):
            try:
                _ = datasource.execute(sql=sql)
                # 如果成功到这里还没挂掉，就是成功了，则流式输出状态
                self.stream_response_openai(content_type='sql',
                                            content=sql,
                                            ignore_tag=True)
                break

            except Exception as e:
                if attempt == MAX_ATTEMPTS:  # 最后一次尝试
                    self.stream_response_openai(content_type='char', content='对不起，遇到了一点问题，请换个问题试试吧')
                    self.stream_response_openai(content_type='stop', content='fail')

                sql = self.repair(error_info=str(e),
                                  query=query,
                                  data_source=datasource)

        # SQL存入历史
        if self.memory:
            self.memory.add_memory(prompt_id='sql', content={'用户问题': query, "SQL": sql})

        return sql

    def repair(self,
               error_info: str,
               query: str,
               data_source: DataSource) -> str:
        """
        重写错误的SQL
        :param error_info: 数据库的报错信息
        :param query: 用户的问题
        :param data_source: 数据源
        :return: 重写后的SQL
        """

        prompt_path = 'chatbi/agent_modules/nl2sql/prompts/rewrite_sql.tmpl'

        rewrite_prompt = self.create_prompt(template_path=prompt_path,
                                            template_desc='重写错误的SQL')

        schema = data_source.adaptive_schema(query=query)

        rewrite_prompt.update_placeholder(
            error=error_info,
            schema=schema
        )

        sql = rewrite_prompt.call(query=query, is_stream=False)  # 不需要流式

        # 将SQL从大模型的输出中抽取出来
        sql: str = SQLOptimizer().extract_sql(sql=sql)

        return sql
