"""
DataFrame修改智能体
可用于改写表格
"""

from chatbi.private.framework import FoundationLayer
import os
import pandas as pd
import re
from chatbi.data_source.base import DataSource
from chatbi.utils.code_executor import run_python_script


class DataFrameModifierAgent(FoundationLayer):
    def __init__(self,
                 proj_path: str = '',
                 **kwargs):

        """
        Args:
            proj_path: 项目的路径，用来拼接提示词的绝对路径
            **kwargs:
        """

        super(DataFrameModifierAgent, self).__init__(**kwargs)

        self.proj_path = proj_path
        self.config.llm.callback = self.config.callback

        # 拼接提示词路径
        self.pandas_coder_prompt_path = os.path.join(self.proj_path,
                                                     'chatbi/agent_modules/edit_df/prompts/pandas_coder.tmpl')

        pass

    @staticmethod
    def _extract_code(text):
        pattern = r'```python\s+([\s\S]+?)\s+```'  # 匹配 python 代码块中的内容
        match = re.search(pattern, text)
        if match:
            return match.group(1).strip()  # 提取并去掉多余的空格
        return 'df = df'

    def modify_dataframe(self,
                         query: str,
                         dataframe: pd.DataFrame) -> pd.DataFrame:
        """
        修改DataFrame，根据用户的需求
        Args:
            dataframe:
            query:用户的问题

        Returns: 新的DataFrame

        """
        df = dataframe.copy()
        df_head = df.head(4)
        df_info = f"DataFrame with {len(df)} rows and {len(df.columns)} columns\n" + str(df.dtypes)

        pandas_prompt = self.create_prompt(template_path=self.pandas_coder_prompt_path,
                                           template_desc='编写Pandas代码')

        pandas_prompt = pandas_prompt.update_placeholder(df_head=df_head,
                                                         df_info=df_info)

        # 这里写pandas就不弄成流式了
        python_code = pandas_prompt.call(query=query, is_stream=False)

        python_code = self._extract_code(python_code)

        new_df = None

        try:
            new_df = run_python_script(script_content=python_code,
                                       input_dataframe=df,
                                       result_var_name="df")
        except Exception as e:
            print(e)

        return new_df

    @staticmethod
    def load_dataframe_from_source(table_name: str = None,
                                   data_source: DataSource = None) -> pd.DataFrame:
        """
        读取DataSource的数据，并将时间进行转换；
        Args:
            table_name:
        Returns:
        :param table_name:
        :param data_source:
        """
        try:
            sql = f'SELECT * FROM {table_name}'
            df = data_source.execute(sql=sql)
        except Exception as e:
            raise e

        # 获取时间信息
        date_info = {}

        for table in data_source.tables:
            if table.name_en != table_name:
                continue

            for column in table.columns:
                if column.is_datetime:
                    date_info[column.name_en] = column.datetime_pattern

        for col, pattern in date_info.items():
            if col in df.columns:
                df[col] = pd.to_datetime(df[col], format=pattern, errors='coerce')

        return df


if __name__ == '__main__':
    from chatbi.llms.chatbi_llm import ChatBILLM
    from chatbi.data_source.dbms.excel_datasource import ExcelDataSource

    excel_ds = ExcelDataSource(data_path='/Users/<USER>/Code/chatbi_backend/example/data/航空公司航班信息表.xlsx',
                               source='test')

    LLM_API_KEY = 'sk-68ac5f5ccf3540ba834deeeaecb48987'
    LLM_BASE_URL = "https://dashscope.aliyuncs.com/compatible-mode/v1"
    MODEL_NAME = "qwen-plus"

    llm = ChatBILLM(base_url=LLM_BASE_URL,
                    api_key=LLM_API_KEY,
                    model_name=MODEL_NAME)

    ta = DataFrameModifierAgent(model=llm, verbose=True)
    df = ta.load_dataframe_from_source(data_source=excel_ds, table_name='航空公司航班信息表')

    new_dataframe = ta.modify_dataframe(query='只保留目的地是东京的数据，并在最后一列加上"2025东京"',
                                        dataframe=df)
