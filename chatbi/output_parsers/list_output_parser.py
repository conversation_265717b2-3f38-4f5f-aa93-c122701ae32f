from chatbi.output_parsers.base import BaseParser
from typing import List, Any, Optional
import re


class ListOutputParser(BaseParser[List[Any]]):
    def __init__(self, item_type: type = str, allow_empty: bool = False) -> None:
        """
        初始化ListOutputParser
        :param item_type: 列表项的预期类型（默认为str）
        :param allow_empty: 是否允许空列表
        """
        self.item_type = item_type
        self.allow_empty = allow_empty

    def preprocess(self, input_str: Any) -> Any:
        """预处理：从输入字符串中提取最左侧和最右侧的 `[` 和 `]` 内的内容，并保留 `[]`"""
        text = str(input_str)
        match = re.search(r"\[(.*?)\]", text, re.DOTALL)
        if match:
            return f"[{match.group(1)}]"  # 返回带有方括号的内容
        return ""  # 如果没有找到有效的列表，则返回空字符串

    def _extract_list_content(self, text: str) -> str:
        """从文本中提取列表内容"""
        import re

        # 处理带前缀的列表格式
        list_patterns = [
            r"\[(.*?)\]",  # 标准列表格式 [item1, item2]
            r"(?:List|列表|清单)[：:,]?\s*\[(.*?)\]",  # 带前缀的列表
            r"[\[(](.*?)[\])]",  # 宽松的括号匹配
        ]

        for pattern in list_patterns:
            match = re.search(pattern, text, re.DOTALL | re.IGNORECASE)
            if match:
                return match.group(1) if pattern.count('(') == 1 else match.group(1)

        # 处理其他格式
        other_patterns = [
            r"(?:\d+\.\s*(.*?)(?=\d+\.|$))",  # 编号列表
            r"(?:-\s*(.*?)(?=-|$))"  # 破折号列表
        ]

        for pattern in other_patterns:
            matches = re.findall(pattern, text, re.DOTALL)
            if matches:
                return ','.join(matches)

        return text

    def _clean_list_item(self, item: str) -> str:
        """清理列表项"""
        import re
        # 移除引号和空白字符
        item = re.sub(r'^[\s\'"`]+|[\s\'"`]+$', '', item)
        # 清理空白字符
        item = re.sub(r'\s+', ' ', item)
        return item.strip()

    def _parse_list_items(self, list_content: str) -> List[Any]:
        """解析列表内容为实际列表项"""
        items = list_content.split(',')
        items = [item.strip() for item in items]
        return [self._convert_item_type(item) for item in items]

    def _convert_item_type(self, item: str) -> Any:
        """将每个列表项转换为预期的类型"""
        try:
            if self.item_type == str:
                return item
            elif self.item_type == int:
                return int(item)
            elif self.item_type == float:
                return float(item)
            else:
                raise ValueError(f"Unsupported item type: {self.item_type}")
        except ValueError as e:
            print(f"转换错误: {e}, 原始值: {item}")
            return None

    def parse(self, input_str: Any) -> List[Any]:

        """解析输入为列表"""
        if isinstance(input_str, list):
            return input_str

        if not isinstance(input_str, str):
            raise ValueError(f"无法解析的输入类型: {type(input_str)}")

        list_content = self.preprocess(input_str)  # 使用预处理方法

        if not list_content:
            raise ValueError(f"未能从输入中提取到有效的列表内容: {input_str}")

        # items = self._parse_list_items(list_content)

        return eval(list_content)

    def validate(self, data: List[Any]) -> bool:
        """验证解析结果"""
        if not isinstance(data, list):
            print("验证失败: 输出不是列表类型")
            return False

        if not self.allow_empty and len(data) == 0:
            print("验证失败: 列表为空")
            return False

        # 验证每个项的类型
        for item in data:
            if not isinstance(item, self.item_type):
                print(f"验证失败: 列表项类型错误: 期望 {self.item_type}, 实际 {type(item)}")
                return False

        return True


if __name__ == '__main__':
    parser = ListOutputParser(item_type=str, allow_empty=False)

    test_cases = [
        "这是你的List， [\"A\", \"B\", \"C\", 'd']",
        "[item1, item2, item3]",
        "列表：['apple', 'banana', 'orange']",
        "1. First item 2. Second item",
        "- Item one - Item two",
        "item1, item2, item3",
    ]

    print("开始测试ListOutputParser...")
    print("-" * 50)

    for test_input in test_cases:
        print(f"\n测试输入: {test_input}")
        try:
            result = parser(test_input)
            print(f"解析结果: {result}")
        except Exception as e:
            print(f"错误: {str(e)}")

    print("\n测试完成")
