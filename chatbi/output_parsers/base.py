from typing import TypeVar, Generic, Any
from abc import ABC, abstractmethod

T = TypeVar('T')


class BaseParser(Generic[T], ABC):
    def preprocess(self, input_str: Any) -> Any:
        """预处理"""
        return input_str

    @abstractmethod
    def parse(self, input_str: Any) -> Any:
        """ 主逻辑 """
        raise NotImplementedError('子类必须实现parse方法')

    def postprocess(self, data: Any) -> Any:
        """后处理"""
        return data

    @abstractmethod
    def validate(self, data: Any) -> bool:
        """验证解析后的数据，确保满足特定的格式,子类根据需要重写此方法。"""
        raise NotImplementedError('子类必须实现validate方法')

    def get_correct_parsed_data(self, data: Any) -> Any:
        return data

    def __call__(self, input_str: Any) -> T:
        from chatbi.utils.printf import printf
        preprocessed_data = self.preprocess(input_str)
        try:
            parsed_data = self.parse(preprocessed_data)
            printf(color='green', title='JsonParser成功解析', message=f"{preprocessed_data}")
        except Exception as e:
            printf(color='red', title='JsonParser解析失败，正在进入纠错', message=f"{preprocessed_data}\n{e}")
            parsed_data = self.get_correct_parsed_data(preprocessed_data)
        if not self.validate(parsed_data):
            return self.get_correct_parsed_data(parsed_data)
        return self.postprocess(parsed_data)
