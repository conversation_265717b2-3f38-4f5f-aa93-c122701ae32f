from chatbi.output_parsers.base import BaseParser
import json
from typing import Any, Dict
from chatbi.utils.printf import printf
import re
from chatbi.output_parsers.prompts.parser_prompts import CorrectJsonParser


def extract_first_json_block(text):
    """
    从文本中提取第一个 ```json 块的内容。
    """
    pattern = r"```json\s*(\{.*?\})\s*```"
    match = re.search(pattern, text, re.DOTALL)
    if match:
        return match.group(1)
    else:
        return text


class JsonOutputParser(BaseParser[Dict[str, Any]]):
    def __init__(self, required: Dict[str, Any] = None) -> None:
        self.required = required
        self.is_valid = False
        pass

    def preprocess(self, input_str: Any) -> str:
        input_str = extract_first_json_block(input_str)
        temp = input_str.strip('```')
        temp = temp.strip('```json')
        return temp

    def parse(self, input_str: str) -> Dict[str, Any]:
        """解析 JSON 字符串到字典."""
        input_str = self.preprocess(input_str)
        try:
            return json.loads(input_str)
        except json.JSONDecodeError as e:
            raise ValueError(f"解析 JSON 失败: {str(e)}")

    def validate(self, data: Dict[str, Any]) -> bool:
        """验证解析后的字典是否包含所有必需的字段和正确的类型."""
        if self.required:
            for key, expected_type in self.required.items():
                if key not in data:
                    printf(color='red', title='转换为Json格式失败', message='key not in data')
                    self.is_valid = False
                    return False
                if expected_type is Any:
                    return True
                if not isinstance(data[key], expected_type):
                    printf(color='red', title='转换为Json格式失败', message='not json instance')
                    self.is_valid = False
                    return False
        printf(color='green', title='成功转换为Json格式', message=f'{data}')
        self.is_valid = True
        return True

    def get_correct_parsed_data(self, data: Any) -> Any:
        printf(color='blue', title='开始纠正Json格式')

        attempt = 0
        while not self.is_valid and attempt <= 5:
            data = CorrectJsonParser(required_format=self.required).call(query=data)
            attempt += 1
            try:
                self.validate(self.parse(data))
            except:
                pass
        return self.parse(data)


if __name__ == '__main__':
    required_fields = {
        "最匹配指标": str,
        "候选指标": str,
    }

    parser = JsonOutputParser(required_fields)

