from elasticsearch import Elasticsearch, helpers
from elasticsearch.exceptions import ConnectionError, NotFoundError, TransportError
from typing import List, Dict, Optional, Any
import json
from chatbi.utils.printf import printf


class ElasticsearchClient:
    def __init__(
            self,
            host: str = None,
            port: int = 9200,
            request_timeout: int = 30,
            user: str = None,
            password: str = None,
            use_https: bool = False,
            verify_certs: bool = True
    ):
        """
        初始化 Elasticsearch 客户端
        Args:
            host: ES server host
            port: Es server port
            request_timeout: 超时时间
            user: 非必填、用户名
            password: 非必填 密码
            use_https: 是否https 默认False
            verify_certs: 是否证书 默认true
        """

        if not host:
            raise ValueError("Elasticsearch server host cannot be empty")

        self.host = host
        self.port = port
        self.request_timeout = request_timeout
        self.user = user
        self.password = password
        self.use_https = use_https
        self.verify_certs = verify_certs

        scheme = "https" if use_https else "http"
        try:
            if user and password:
                self.es = Elasticsearch(
                    [f"{scheme}://{host}:{port}"],
                    basic_auth=(user, password),
                    request_timeout=request_timeout,
                    verify_certs=verify_certs
                )
            else:
                self.es = Elasticsearch(
                    [f"{scheme}://{host}:{port}"],
                    request_timeout=request_timeout,
                    verify_certs=verify_certs
                )

            if self.es.ping():
                printf(color='green', title='Elasticsearch连接成功', message=f'Elasticsearch服务器 {host}:{port} 可用')
            else:
                printf(color='red', title='Elasticsearch连接失败', message=f'Elasticsearch服务器 {host}:{port} 不可用')
                self.es = None
        except (ConnectionError, TransportError) as e:
            self.es = None
            printf(color='red', title='Elasticsearch连接错误', message=f"连接到Elasticsearch服务器时发生错误: {e}")
        except Exception as e:
            self.es = None
            printf(color='red', title='Elasticsearch连接错误', message=f"连接到Elasticsearch服务器时发生未知错误: {e}")

    def export_config(self) -> Dict[str, Any]:
        config = {'host': self.host,
                  'port': self.port,
                  'request_timeout': self.request_timeout,
                  'user': self.user,
                  'password': self.password,
                  'use_https': self.use_https,
                  'verify_certs': self.verify_certs}
        return config

    def create_index(self, index_name: str, settings: Optional[Dict[str, Any]] = None):
        """
        创建一个新的索引
        Args:
            index_name (str): 索引名称
            settings (Optional[Dict[str, Any]]): 索引设置和映射
        """
        if self.es is None:
            printf(color='red', title='操作失败', message='Elasticsearch客户端未连接')
            return
        try:
            if not self.es.indices.exists(index=index_name):
                body = settings if settings else {}
                self.es.indices.create(index=index_name, body=body)
                printf(color='green', title='索引创建成功', message=f"索引 '{index_name}' 已创建")
            else:
                printf(color='yellow', title='索引已存在', message=f"索引 '{index_name}' 已存在")
        except (TransportError, ConnectionError) as e:
            printf(color='red', title='创建索引失败', message=f"创建索引 '{index_name}' 时发生错误: {e}")
        except Exception as e:
            printf(color='red', title='创建索引失败', message=f"创建索引 '{index_name}' 时发生未知错误: {e}")

    def delete_index(self, index_name: str) -> None:
        """
        删除指定的索引
        Args:
            index_name: 索引名称
        Returns: None
        """
        if self.es is None:
            printf(color='red', title='操作失败', message='Elasticsearch客户端未连接')
            return
        try:
            if self.es.indices.exists(index=index_name):
                self.es.indices.delete(index=index_name)
                printf(color='green', title='索引删除成功', message=f"索引 '{index_name}' 已删除")
            else:
                printf(color='yellow', title='索引不存在', message=f"索引 '{index_name}' 不存在")
        except (TransportError, ConnectionError) as e:
            printf(color='red', title='删除索引失败', message=f"删除索引 '{index_name}' 时发生错误: {e}")
        except Exception as e:
            printf(color='red', title='删除索引失败', message=f"删除索引 '{index_name}' 时发生未知错误: {e}")

    def index_document(self,
                       index_name: str,
                       doc_id: str,
                       document: Dict[str, Any]) -> None:
        """
        索引一个文档（将文档存储到指定的索引index中）
        Args:
            index_name (str): 索引名称
            doc_id (str): 文档ID
            document (Dict[str, Any]): 文档内容
        """
        if self.es is None:
            printf(color='red', title='操作失败', message='Elasticsearch客户端未连接')
            return
        try:
            self.es.index(index=index_name, id=doc_id, body=document, refresh=True)

        except (TransportError, ConnectionError) as e:
            printf(color='red', title='索引文档失败', message=f"索引文档 ID '{doc_id}' 时发生错误: {e}")
        except Exception as e:
            printf(color='red', title='索引文档失败', message=f"索引文档 ID '{doc_id}' 时发生未知错误: {e}")

    def get_document(self, index_name: str, doc_id: str) -> Optional[Dict[str, Any]]:
        """
        获取指定ID的文档
        Args:
            index_name (str): 索引名称
            doc_id (str): 文档ID
        Returns:
            Optional[Dict[str, Any]]: 文档内容或None
        """
        if self.es is None:
            printf(color='red', title='操作失败', message='Elasticsearch客户端未连接')
            return None
        try:
            result = self.es.get(index=index_name, id=doc_id)
            # printf(color='green', title='获取文档成功', message=f"文档 ID '{doc_id}' 已获取")
            return result['_source']
        except NotFoundError:
            printf(color='yellow', title='文档未找到', message=f"文档 ID '{doc_id}' 在索引 '{index_name}' 中不存在")
            return None
        except (TransportError, ConnectionError) as e:
            printf(color='red', title='获取文档失败', message=f"获取文档 ID '{doc_id}' 时发生错误: {e}")
            return None
        except Exception as e:
            printf(color='red', title='获取文档失败', message=f"获取文档 ID '{doc_id}' 时发生未知错误: {e}")
            return None

    def search_documents(self,
                         index_name: str,
                         query: Dict[str, Any],) -> List[Dict[str, Any]]:
        """
        根据query在指定index中搜索文档
        Args:
            index_name (str): 索引名称
            query (Dict[str, Any]): 搜索查询

        Returns:
            List[Dict[str, Any]]: 匹配的文档列表
        """
        if self.es is None:
            printf(color='red', title='操作失败', message='Elasticsearch客户端未连接')
            return []

        try:
            search_results = self.es.search(index=index_name, body=query)
            hits = search_results['hits']['hits']
            scores = [hit['_score'] for hit in hits]
            result = [hit['_source'] for hit in hits]
            temp = []
            for res, sco in zip(result, scores):
                res['score'] = sco
                temp.append(res)
            return temp

        except Exception as e:
            printf(color='red', title='搜索失败', message=f"搜索索引 '{index_name}' 时发生未知错误: {e}")
            return []

    def update_document(self,
                        index_name: str,
                        doc_id: str,
                        update_body: Dict[str, Any]):
        """
        更新指定ID的文档
        Args:
            index_name (str): 索引名称
            doc_id (str): 文档ID
            update_body (Dict[str, Any]): 更新内容
        """
        if self.es is None:
            printf(color='red', title='操作失败', message='Elasticsearch客户端未连接')
            return
        try:
            self.es.update(index=index_name, id=doc_id, body={"doc": update_body})
            printf(color='green', title='文档更新成功', message=f"文档 ID '{doc_id}' 已成功更新")
        except NotFoundError:
            printf(color='yellow', title='文档未找到', message=f"文档 ID '{doc_id}' 在索引 '{index_name}' 中不存在")
        except (TransportError, ConnectionError) as e:
            printf(color='red', title='更新文档失败', message=f"更新文档 ID '{doc_id}' 时发生错误: {e}")
        except Exception as e:
            printf(color='red', title='更新文档失败', message=f"更新文档 ID '{doc_id}' 时发生未知错误: {e}")

    def delete_document(self, index_name: str, doc_id: str):
        """
        删除指定ID的文档
        Args:
            index_name (str): 索引名称。
            doc_id (str): 文档ID。
        """
        if self.es is None:
            printf(color='red', title='操作失败', message='Elasticsearch客户端未连接')
            return
        try:
            self.es.delete(index=index_name, id=doc_id)
            printf(color='green', title='文档删除成功', message=f"文档 ID '{doc_id}' 已删除")
        except NotFoundError:
            printf(color='yellow', title='文档未找到', message=f"文档 ID '{doc_id}' 在索引 '{index_name}' 中不存在")
        except (TransportError, ConnectionError) as e:
            printf(color='red', title='删除文档失败', message=f"删除文档 ID '{doc_id}' 时发生错误: {e}")
        except Exception as e:
            printf(color='red', title='删除文档失败', message=f"删除文档 ID '{doc_id}' 时发生未知错误: {e}")

    def bulk_index(self, index_name: str, documents: List[Dict[str, Any]]):
        """
        批量索引文档
        Args:
            index_name (str): 索引名称。
            documents (List[Dict[str, Any]]): 文档列表。
        """
        if self.es is None:
            printf(color='red', title='操作失败', message='Elasticsearch客户端未连接')
            return
        actions = []
        for doc in documents:
            action = {
                "_index": index_name,
                "_id": doc.get("id"),
                "_source": doc
            }
            actions.append(action)
        try:
            helpers.bulk(self.es, actions)
            printf(color='green', title='批量索引成功', message=f"已成功批量索引 {len(actions)} 个文档到 '{index_name}'")
        except (TransportError, ConnectionError) as e:
            printf(color='red', title='批量索引失败', message=f"批量索引到 '{index_name}' 时发生错误: {e}")
        except Exception as e:
            printf(color='red', title='批量索引失败', message=f"批量索引到 '{index_name}' 时发生未知错误: {e}")

    def get_all_indices(self) -> List[str]:
        """
        获取所有索引名称
        Returns:
            List[str]: 索引名称列表
        """
        if self.es is None:
            printf(color='red', title='操作失败', message='Elasticsearch客户端未连接')
            return []
        try:
            # 使用 cat.indices API 获取所有索引
            indices_info = self.es.cat.indices(format='json')
            index_names = [index['index'] for index in indices_info]
            printf(color='green', title='获取所有索引成功', message=f"找到 {len(index_names)} 个索引")
            return index_names
        except (TransportError, ConnectionError) as e:
            printf(color='red', title='获取索引失败', message=f"获取所有索引时发生错误: {e}")
            return []
        except Exception as e:
            printf(color='red', title='获取索引失败', message=f"获取所有索引时发生未知错误: {e}")
            return []

    def get_cluster_health(self) -> Optional[Dict[str, Any]]:
        """
        获取集群健康状态
        Returns:
            Optional[Dict[str, Any]]: 集群健康信息或 None
        """
        if self.es is None:
            printf(color='red', title='操作失败', message='Elasticsearch客户端未连接')
            return None
        try:
            health = self.es.cluster.health()
            printf(color='green', title='集群健康状态', message=health)
            return health
        except (TransportError, ConnectionError) as e:
            printf(color='red', title='获取集群健康状态失败', message=f"发生错误: {e}")
            return None
        except Exception as e:
            printf(color='red', title='获取集群健康状态失败', message=f"发生未知错误: {e}")
            return None

    def search_vector(self, index_name: str, vector: List[float], k: int = 10) -> List[Dict[str, Any]]:
        """
        执行向量检索
        Args:
            index_name (str): 索引名称
            vector (List[float]): 查询向量
            k (int): 返回的最近邻数量

        Returns:
            List[Dict[str, Any]]: 匹配的文档列表。
        """
        if self.es is None:
            printf(color='red', title='操作失败', message='Elasticsearch客户端未连接')
            return []
        try:
            query = {
                "size": k,
                "query": {
                    "script_score": {
                        "query": {
                            "match_all": {}
                        },
                        "script": {
                            "source": "cosineSimilarity(params.query_vector, 'vector') + 1.0",
                            "params": {
                                "query_vector": vector
                            }
                        }
                    }
                }
            }
            results = self.es.search(index=index_name, body=query)
            hits = results['hits']['hits']
            printf(color='green', title='向量搜索成功', message=f"找到 {len(hits)} 个匹配文档")
            return [hit['_source'] for hit in hits]
        except (TransportError, ConnectionError) as e:
            printf(color='red', title='向量搜索失败', message=f"在索引 '{index_name}' 上执行向量搜索时发生错误: {e}")
            return []
        except Exception as e:
            printf(color='red', title='向量搜索失败', message=f"在索引 '{index_name}' 上执行向量搜索时发生未知错误: {e}")
            return []

    def close(self):
        """关闭客户端连接"""
        if self.es:
            self.es.transport.close()
            printf(color='green', title='客户端关闭', message='Elasticsearch客户端连接已关闭')

    def get_all_documents(self, index_name: str) -> List[Dict[str, Any]]:
        """
        获取指定索引中的所有文档
        Args:
            index_name (str): 索引名称
        Returns:
            List[Dict[str, Any]]: 文档列表
        """
        if self.es is None:
            printf(color='red', title='操作失败', message='Elasticsearch客户端未连接')
            return []
        try:
            # 使用 scroll API 获取所有文档
            query = {
                "query": {
                    "match_all": {}
                },
                "size": 1000  # 每次返回1000个文档
            }
            results = self.es.search(index=index_name, body=query, scroll='2m')
            hits = results['hits']['hits']
            scroll_id = results['_scroll_id']

            all_hits = hits.copy()
            while len(hits) > 0:
                results = self.es.scroll(scroll_id=scroll_id, scroll='2m')
                hits = results['hits']['hits']
                all_hits.extend(hits)

            printf(color='green', title='获取所有文档成功', message=f"找到 {len(all_hits)} 个文档")
            return [hit['_source'] for hit in all_hits]
        except (TransportError, ConnectionError) as e:
            printf(color='red', title='获取所有文档失败', message=f"获取索引 '{index_name}' 中的所有文档时发生错误: {e}")
            return []
        except Exception as e:
            printf(color='red', title='获取所有文档失败', message=f"获取索引 '{index_name}' 中的所有文档时发生未知错误: {e}")
            return []

    def get_all_doc_ids(self, index_name: str) -> List[str]:
        """
        获取指定索引中的所有文档ID
        """
        if self.es is None:
            printf(color='red', title='操作失败', message='Elasticsearch客户端未连接')
            return []
        try:
            # 使用 scroll API 获取所有文档的ID
            query = {
                "_source": False,  # 不返回文档内容
                "query": {
                    "match_all": {}
                },
                "size": 1000  # 每次返回1000个文档ID
            }
            results = self.es.search(index=index_name, body=query, scroll='2m')
            hits = results['hits']['hits']
            scroll_id = results['_scroll_id']

            all_doc_ids = [hit['_id'] for hit in hits]

            while len(hits) > 0:
                results = self.es.scroll(scroll_id=scroll_id, scroll='2m')
                hits = results['hits']['hits']
                all_doc_ids.extend([hit['_id'] for hit in hits])

            printf(color='green', title='获取所有文档ID成功', message=f"找到 {len(all_doc_ids)} 个文档ID")
            return all_doc_ids
        except (TransportError, ConnectionError) as e:
            printf(color='red', title='获取文档ID失败', message=f"获取索引 '{index_name}' 中的文档ID时发生错误: {e}")
            return []
        except Exception as e:
            printf(color='red', title='获取文档ID失败', message=f"获取索引 '{index_name}' 中的文档ID时发生未知错误: {e}")
            return []

    def delete_by_query(self, index_name: str, query: Dict[str, Any]) -> Dict[str, Any]:
        return self.es.delete_by_query(index=index_name, body=query)

    def __str__(self):
        return f"ElasticsearchClient(host={self.host}, port={self.port}, request_timeout={self.request_timeout})"


if __name__ == "__main__":
    client = ElasticsearchClient(
        host="************",
        port=9200,
        request_timeout=10
    )

    # 获取集群健康状态
    cluster_health = client.get_cluster_health()

    # 删除索引
    # client.delete_index("my_index_a")

    print('获取索引下所有的doc id')
    print(client.get_all_doc_ids(index_name='enums_test_excel'))

    # 创建索引，包含向量字段，并添加必要的参数
    index_settings = {
        "settings": {
            "number_of_shards": 1,
            "number_of_replicas": 0
        },
        "mappings": {
            "properties": {
                "title": {"type": "text"},
                "content": {"type": "text"},
                "vector": {
                    "type": "dense_vector",
                    "dims": 8,
                    "index": True,
                    "similarity": "cosine"
                }
            }
        }
    }
    client.create_index("my_index_a", settings=index_settings)

    # 索引文档，包含向量
    doc = {
        "title": "测试",
        "content": "这是一个测试内容",
        "vector": [0.1] * 8,  # 示例向量，实际使用中请替换为真实向量
        "id": "1"
    }
    client.index_document("my_index_a", "1", doc)

    # 确认文档已正确索引
    document = client.get_document("my_index_a", "1")
    print("获取到的文档内容:", json.dumps(document, ensure_ascii=False, indent=2))

    # 搜索文档
    query = {
        "query": {
            "match": {
                "content": "这"
            }
        }
    }
    results = client.search_documents("my_index_a", query)
    print("搜索结果:", json.dumps(results, ensure_ascii=False, indent=2))

    # 向量检索
    query_vector = [0.1] * 8
    vector_results = client.search_vector("my_index_a", query_vector, k=5)
    print("向量搜索结果:", json.dumps(vector_results, ensure_ascii=False, indent=2))

    # 更新文档
    update_body = {"content": "这是一个更新后的测试内容"}
    client.update_document("my_index_a", "1", update_body)

    # 获取更新后的文档
    updated_document = client.get_document("my_index_a", "1")
    print("更新后的文档内容:", json.dumps(updated_document, ensure_ascii=False, indent=2))

    print('获取索引下所有的doc id')
    print(client.get_all_doc_ids(index_name='my_index_a'))

    # 删除文档
    client.delete_document("my_index_a", "1")

    # 删除索引
    client.delete_index("my_index_a")

    # 关闭客户端
    client.close()
