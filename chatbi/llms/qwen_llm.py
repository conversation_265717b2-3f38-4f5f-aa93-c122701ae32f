"""
这里就不掺和磐智了，纯阿里API调用.
调用磐智请用 PanZhiLLM。

模型列表：
https://help.aliyun.com/zh/model-studio/getting-started/models?spm=a2c4g.11186623.help-menu-2400256.d_0_2.4d891d1cNBm4e5&scm=20140722.H_2840914._.OR_help-V_1#9f8890ce29g5u
"""

from chatbi.llms.base import BaseLLM, retry_on_exception, log_response_info
from typing import Union, List, Dict, Mapping, Optional
from chatbi.exceptions import (
    APIError,
    APIParameterError,
    APIAuthenticationError,
    APIInvalidResponse
)
from openai import OpenAI, AsyncOpenAI
import os
from time import time

from chatbi.utils.printf import printf
import json


class QwenLLM(BaseLLM):
    def __init__(self,
                 api_key: str = None,
                 model_name: str = "qwen-plus",
                 base_url: str = "https://dashscope.aliyuncs.com/compatible-mode/v1",
                 header: Mapping[str, str] = None,
                 temperature: float = 0,
                 max_tokens: int = 2000,
                 top_p: float = 1.0):
        """
        Args:
            api_key: 密钥，如空则从环境变量 DASHSCOPE_API_KEY 中获取
            model_name: 模型名称
            base_url: API URL
            temperature: 温度，0~1
            max_tokens: 生成内容最大长度
            top_p: 生成采样的控制参数
        """
        super().__init__()
        self.api_key = api_key or os.getenv("LLM_API_KEY")
        if not self.api_key:
            raise APIAuthenticationError("API 密钥未提供，请在初始化时提供或设置环境变量 LLM_API_KEY")
        self.model_name = model_name
        self.base_url = base_url
        self.header = header
        self.temperature = temperature
        self.max_tokens = max_tokens
        self.top_p = top_p

        self.client = OpenAI(
            api_key=self.api_key,
            base_url=self.base_url,
            default_headers=self.header
        )

        self.async_client = AsyncOpenAI(
            api_key=self.api_key,
            base_url=self.base_url,
            default_headers=self.header
        )

    @retry_on_exception(max_retries=5, delay=0.2, exception=Exception)
    @log_response_info
    def _get_llm_response(self,
                          prompt: Union[str, List[Dict]]) -> str:
        try:
            # 检查 prompt 类型
            if isinstance(prompt, str):
                messages = [{'role': 'user', 'content': prompt}]
            elif isinstance(prompt, list):
                messages = prompt
            else:
                raise APIParameterError("prompt 必须是字符串或包含消息的列表")

            start_time = time()
            # print(f' debug {self.model_name}')
            completion = self.client.chat.completions.create(
                model=self.model_name,
                messages=messages,
                temperature=self.temperature,
                max_tokens=self.max_tokens,
                top_p=self.top_p,
                timeout=9999,
            )

            end_time = time()
            time_taken = round(end_time - start_time, 2)

            if completion and completion.choices:
                content = completion.choices[0].message.content.strip()
                if content:

                    self._response_info = {
                        'usage': dict(completion.usage),
                        'model_name': self.model_name,
                        'time_taken': time_taken,
                    }
                    return content
                else:
                    raise APIInvalidResponse("Qwen API 返回的响应内容为空")
            else:
                raise APIInvalidResponse("Qwen API 返回的响应格式不正确")

        except APIAuthenticationError as e:
            raise e

        except APIError as e:
            raise e

    def set_temperature(self, temperature: float):
        if 0.0 <= temperature <= 1.0:
            self.temperature = temperature
        else:
            raise APIParameterError("温度必须在 0 到 1 之间")

    def set_max_tokens(self, max_tokens: int):
        if max_tokens > 0:
            self.max_tokens = max_tokens
        else:
            raise APIParameterError("max_tokens 必须大于 0")

    def set_top_p(self, top_p: float):
        if 0.0 <= top_p <= 1.0:
            self.top_p = top_p
        else:
            raise APIParameterError("top_p 必须在 0 到 1 之间")

    def set_model_name(self, model_name: str):
        self.model_name = model_name

    def set_base_url(self, base_url: str):
        self.base_url = base_url
        # 重新初始化客户端
        self.client = OpenAI(
            api_key=self.api_key,
            base_url=self.base_url,
        )

    def get_streaming_response(self, prompt: Union[str, List[Dict]]):
        """
        获取流式响应
        Args:
            prompt: query
        Yields:
            从流中逐步获取并输出内容
        """
        try:
            # 检查 prompt 类型
            if isinstance(prompt, str):
                messages = [{'role': 'user', 'content': prompt}]
            elif isinstance(prompt, list):
                messages = prompt
            else:
                raise APIParameterError("prompt 必须是字符串或包含消息的列表")

            completion = self.client.chat.completions.create(
                model=self.model_name,
                messages=messages,
                temperature=self.temperature,
                max_tokens=self.max_tokens,
                top_p=self.top_p,
                stream=True
            )

            # 逐步处理并输出流数据
            for chunk in completion:
                # print(chunk.model_dump_json())
                yield chunk.model_dump_json()

        except APIAuthenticationError as e:
            raise e

        except APIError as e:
            raise e

    def function_calling(self,
                         role_setting: str = None,
                         prompt: Union[str, List[Dict]] = None,
                         tools: List[Dict] = None,
                         messages: List[Dict] = None) -> Optional[Dict]:
        """
        Qwen 调用工具
        Args:
            role_setting: 系统角色设定
            prompt: 用户输入的内容或历史对话记录
            tools: 工具信息列表
            messages: 拼装好的历史消息
        Returns: Optional[Dict]
            如果有工具调用需求，则返回包含工具名称和参数的字典
            Example:
            {'invoke_tool': True,
             'content': '内容',
             'func': {'name': 'get_current_weather', 'args': {'location': '杭州市'}}}
        """
        if not prompt and not messages:
            raise ValueError("Prompt or messages must be provided.")

        # 优化消息拼装逻辑
        if prompt and role_setting:
            messages = [{"role": "system", "content": role_setting},
                        {"role": "user", "content": prompt}]
        elif prompt:
            messages = [{"role": "user", "content": prompt}]

        try:
            completion = self.client.chat.completions.create(
                model=self.model_name,
                messages=messages,
                tools=tools,
            ).model_dump()

            assistant_output = completion['choices'][0]['message']
            # printf(color='green', title='Function Calling', message=str(assistant_output))

            # 判断是否需要工具调用
            tool_calls = assistant_output.get('tool_calls')
            if not tool_calls:
                return {
                    'invoke_tool': False,
                    'content': assistant_output.get('content', ''),
                    'func': None
                }

            # 解析工具调用
            tool_call = tool_calls[0]
            func_name = tool_call.get('function', {}).get('name')
            func_args = json.loads(tool_call.get('function', {}).get('arguments', '{}'))

            return {
                'invoke_tool': True,
                'content': assistant_output.get('content', ''),
                'func': {'name': func_name, 'args': func_args}
            }

        except APIAuthenticationError as e:
            printf(color='red', title='大模型 Function Calling 失败', message=str(e))
            return None

        except Exception as e:
            printf(color='red', title='Function Calling 出现异常', message=str(e))
            return None

    def __repr__(self):
        return f"Qwen(api_key='****', model_name='{self.model_name}', base_url='{self.base_url}', temperature={self.temperature}, max_tokens={self.max_tokens}, top_p={self.top_p})"

