from abc import abstractmethod
from jinja2 import Template
from uuid import uuid4
from typing import List, Dict, Union
from functools import wraps
from chatbi.utils.printf import printf
import time


def retry_on_exception(max_retries=5, delay=0, exception=Exception):
    """
    修饰器，用于在指定方法抛出异常时进行重试
    :param max_retries: 最大重试次数
    :param delay: 重试时延，避免把服务器打爆了
    :param exception: 默认捕获所有异常，后面针对特定异常进行补货
    """

    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            retries = 0
            while retries < max_retries:
                try:
                    return func(*args, **kwargs)
                except exception as e:
                    retries += 1
                    printf(title='调用基座模型失败，重试中', color='red',
                           message=f"Attempt {retries} failed with exception: {e}. Retrying...")
                    time.sleep(delay)
            return func(*args, **kwargs)

        return wrapper

    return decorator


def log_response_info(func):
    @wraps(func)
    def wrapper(self, *args, **kwargs):
        start_time = time.time()
        response = func(self, *args, **kwargs)
        end_time = time.time()

        # 计算耗时
        time_taken = round(end_time - start_time, 2)
        model_name = getattr(self, "model_name", "Unknown Model")
        url = getattr(self, "base_url", "Unknown URL")

        self._response_info['time_taken'] = time_taken
        tokens_info = self._response_info.get('usage', {})

        # 计算 TPS
        total_tokens = tokens_info.get('total_tokens', 0)
        tps = round(total_tokens / time_taken, 2) if time_taken > 0 else "N/A"

        # 输出模型的信息（调试的时候用，可注释掉）
        printf(
            title='LLM Response Info',
            color='green',
            message=f'{"=" * 40}\n{response}\n{"=" * 40}\nModel: {model_name}\nTime taken: {time_taken} seconds\n'
                    f'Tokens used: {tokens_info}\nUrl: {url}\nTPS: {tps} tokens/second\n{"=" * 40}'
        )

        infos = {'response': response, 'time_taken': time_taken, 'model_name': model_name, 'tokens_used': tokens_info,
                 'url': url, 'tps': tps, 'trace_id': self.trace_id, 'ins': self.instruction}

        self.response_infos.append(infos)  # 这里给后面FoundationLayer记录日志用

        return response

    return wrapper


class BaseLLM:
    def __init__(self, max_retries=5, delay=0):
        """max_retries, delay 分别是重试次数、延迟"""
        self.max_retries = max_retries
        self.delay = delay
        self.uid = uuid4()
        self._response_info = {}
        self.response_infos = []  # 里面存的是_response_info
        self.trace_id = None  # 与后面FoundationLayer联动

        self.instruction = None  # 记录日志用，记录大模型的prompts

        self.is_skipping_firewall = False  # 为了防止智能审查的时候死循环调用，详见 llm_base_firewall.py

        self.callback = None  # 实现字符级流式，利用DataStreamer

        self.verbose = False  # 控制是否每次调用大模型都输出prompts
        pass

    def set_trace_id(self, trace_id):
        self.response_infos = []
        self.trace_id = trace_id
        pass

    @abstractmethod
    def _get_llm_response(self,
                          prompt: Union[str, List[Dict]],
                          image_b64: str = None,
                          image_type: str = None) -> str:
        """
        异步方法：将prompt传入大模型中获得response
        此方法需要在子类中重写
        """
        raise NotImplementedError("Call method has not been implemented")

    def call(self,
             instruction: 'BasePrompt' = None,
             query: str = None,
             image_b64: str = None,
             image_type: str = None) -> str:

        """同步调用方法"""
        instruction = instruction.render()
        if query:
            instruction = Template(instruction)
            instruction = instruction.render(query=query)

        resp = self._get_llm_response(instruction, image_b64=image_b64, image_type=image_type)
        return resp

    def function_calling(self):
        """Function Calling"""
        pass

    def call_with_specified_text(self,
                                 instruction: 'BasePrompt' = None,
                                 query: str = None,
                                 start_token: str = '',
                                 end_token: str = '') -> str:
        """获取特定字符之间的文本"""
        import re
        text = self.call(instruction, query)
        # 构建正则表达式，匹配起始标识符和结束标识符之间的内容
        pattern = re.compile(re.escape(start_token) + "\n(.*?)\n" + re.escape(end_token))
        pattern = re.compile(r'```[\w+-]*\n(.*?)```', re.DOTALL)

        # match = pattern.search(text)
        matches = pattern.findall(text)
        if matches:
            return matches[-1]  # 返回生成的最后一段 ```xxxx```
        else:
            return ''

    def invoke(self,
               query,
               image_b64: str = None,
               image_type: str = None):

        return self._get_llm_response(query, image_b64=image_b64, image_type=image_type)

    def get_response_info(self) -> Dict:
        """用于返回大模型API的其余信息"""
        return self._response_info

    def get_streaming_response(self,
                               prompt: Union[str, List[Dict]],
                               image_b64: str = None,
                               image_type: str = None):
        """获取流式响应，如果不支持，可以不实现"""
        raise NotImplementedError("This LLM does not support streaming responses.")

    def __add__(self, other):
        """
        实现加法运算符，支持 LLM 实例相加
        支持以下情况：
        1. LLM + LLM -> LLMBalancer
        2. LLM + LLMBalancer -> LLMBalancer
        """
        from chatbi.llms.balancer import LLMBalancer
        if isinstance(other, BaseLLM):
            return LLMBalancer([self, other])
        elif isinstance(other, LLMBalancer):
            other.add_llm(self)
            return other
        else:
            raise TypeError(f"不支持与{type(other)}类型进行加法操作")

    def __radd__(self, other):
        """
        实现反向加法运算符，处理以下情况：
        1. LLMBalancer + LLM -> LLMBalancer
        """
        return self.__add__(other)
