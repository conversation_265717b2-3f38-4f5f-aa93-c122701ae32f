from time import time
import base64, hashlib, json, time
from uuid import uuid4
import requests


def get_header(model_name='qwen2-72b-hc'):
    appid = "dsjyybai"
    capabilityname = model_name
    appKey = "4614ffb405668e4dcdaa00f288dddcd6"
    csid = appid+capabilityname+"0"*(24-len(capabilityname))+str(uuid4()).replace("-", "")
    x_server_param = base64.urlsafe_b64encode(json.dumps({"appid": appid, "csid": csid}).encode())
    curtime = int(time.time())
    checkSum = hashlib.md5(f"{appKey}{str(curtime)}{x_server_param.decode()}".encode('utf-8')).hexdigest()

    headers = {
        "X-Server-Param": f"{x_server_param.decode()}",
        "X-CurTime": f"{curtime}",
        "X-CheckSum": checkSum,
    }
    return headers


def check_internet_connection(url='http://www.baidu.com', timeout=3):
    try:
        response = requests.get(url, timeout=timeout)
        if response.status_code == 200:
            return True
        else:
            return False
    except requests.ConnectionError:
        return False

# def get_header():
#     panzhi_headers = {
#         'X-Server-Param': 'eyJhcHBpZCI6ICJkc2p5eWJhaSIsICJjc2lkIjogImRzanl5YmFpd3RjaGF0YmlybXMwMDAwMDAwMDAwMDAwYTlkODM0N2I2ZDgyNDJmNTlkNDU1OGQxNTg4NzM0YjUifQ',
#         'X-CurTime': '1718069169',
#         'X-CheckSum': '46c535a98c79c227f707ebe4602763f8'
#     }
#     return panzhi_headers


if __name__ == '__main__':
    url = 'http://10.217.247.48:9050/llmm/v1'
    print(check_internet_connection(url=url))

