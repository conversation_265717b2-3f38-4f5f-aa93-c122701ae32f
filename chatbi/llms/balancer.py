from typing import List, Dict, Union, Optional
from chatbi.llms.base import BaseLLM
import time


class LLMBalancer(BaseLLM):
    """LLM负载均衡调节器，用于提供多个大模型实例给Agent"""

    def __init__(self, llms: Optional[List[BaseLLM]] = None):
        """
        初始化负载均衡调节器
        Args:
            llms: BaseLLM实例列表
        """
        super().__init__()
        self.llms = llms or []
        # 记录每个实例的请求次数和最后请求时间
        self._request_counts = {id(llm): 0 for llm in self.llms}
        self._last_request_time = {id(llm): 0 for llm in self.llms}

    def _select_llm(self) -> BaseLLM:
        """
        核心算法，选择负载最小的LLM实例！
        Returns:
            BaseLLM: 选中的LLM实例
        """
        if not self.llms:
            raise ValueError("没有可用的LLM实例")

        current_time = time.time()
        scores = {}

        # 计算每个实例的得分
        for llm in self.llms:
            llm_id = id(llm)
            request_count = self._request_counts[llm_id]
            time_since_last_request = current_time - self._last_request_time[llm_id]

            # 计算得分：上次请求时间间隔越长、请求次数越少，得分越高
            scores[llm_id] = time_since_last_request / (request_count + 1)

        # 选择得分最高的实例
        selected_id = max(scores, key=scores.get)
        selected_llm = next(llm for llm in self.llms if id(llm) == selected_id)

        # 更新请求计数和时间
        self._request_counts[selected_id] += 1
        self._last_request_time[selected_id] = current_time

        return selected_llm

    def add_llm(self, llm: BaseLLM):
        """
        添加新的LLM实例
        Args:
            llm: BaseLLM实例
        """
        if not isinstance(llm, BaseLLM):
            raise TypeError(f"只能添加BaseLLM实例，不支持{type(llm)}")

        self.llms.append(llm)
        self._request_counts[id(llm)] = 0
        self._last_request_time[id(llm)] = 0

    def __add__(self, other: Union[BaseLLM, 'LLMBalancer']):
        """
        实现加法操作符，支持添加LLM实例或合并两个LLMBalancer
        """
        if isinstance(other, BaseLLM):
            self.add_llm(other)
            return self
        elif isinstance(other, LLMBalancer):
            for llm in other.llms:
                self.add_llm(llm)
            return self
        else:
            raise TypeError(f"不支持与{type(other)}类型进行加法操作")

    def __iadd__(self, other: Union[BaseLLM, 'LLMBalancer']):
        """实现 += 操作符"""
        return self.__add__(other)

    async def _async_get_llm_response(self, prompt: Union[str, List[Dict]]) -> str:
        """
        异步获取LLM响应，会自动选择负载最小的实例
        """
        selected_llm = self._select_llm()
        # 传递相关上下文信息
        selected_llm.instruction = self.instruction
        selected_llm.trace_id = self.trace_id
        return await selected_llm._async_get_llm_response(prompt)

    def _get_llm_response(self, prompt: Union[str, List[Dict]]) -> str:
        """
        同步获取LLM响应，会自动选择负载最小的实例
        """
        selected_llm = self._select_llm()
        selected_llm.instruction = self.instruction
        selected_llm.trace_id = self.trace_id
        return selected_llm._get_llm_response(prompt)

    def get_streaming_response(self, prompt: Union[str, List[Dict]]):
        """获取流式响应"""
        selected_llm = self._select_llm()
        selected_llm.instruction = self.instruction
        selected_llm.trace_id = self.trace_id
        return selected_llm.get_streaming_response(prompt)

    async def async_get_streaming_response(self, prompt: Union[str, List[Dict]]):
        """获取异步流式响应"""
        selected_llm = self._select_llm()
        selected_llm.instruction = self.instruction
        selected_llm.trace_id = self.trace_id
        return await selected_llm.async_get_streaming_response(prompt)

    def __repr__(self):
        return f"LLMBalancer(llms_count={len(self.llms)})"
