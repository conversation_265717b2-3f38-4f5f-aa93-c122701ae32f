from chatbi.llms.base import BaseLLM, retry_on_exception, log_response_info
from typing import Union, List, Dict
from chatbi.exceptions import (
    APIError,
    APIParameterError,
    APIAuthenticationError,
    APIInvalidResponse
)
import os
import requests


class LlamaLLM(BaseLLM):
    def __init__(self,
                 api_key: str = None,
                 model_name: str = "llama2-7b-chat-v2",
                 base_url: str = "https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation",
                 temperature: float = 0.7,
                 max_tokens: int = 1024,
                 top_p: float = 1.0):
        """
        Args:
            api_key: Llama API 的密钥，如果不提供则从环境变量 DASHSCOPE_API_KEY 中获取
            model_name: 要使用的 Llama 模型名称
            base_url: Llama API URL
            temperature: 温度，0~1
            max_tokens: 生成内容最大长度
            top_p: 生成采样的控制参数
        """
        super().__init__()
        self.api_key = api_key or os.getenv("DASHSCOPE_API_KEY")
        if not self.api_key:
            raise APIAuthenticationError("API 密钥未提供，请在初始化时提供或设置环境变量 DASHSCOPE_API_KEY")
        self.model_name = model_name
        self.base_url = base_url
        self.temperature = temperature
        self.max_tokens = max_tokens
        self.top_p = top_p

    @retry_on_exception(max_retries=2, delay=0.7, exception=Exception)
    @log_response_info
    def _get_llm_response(self, prompt: Union[str, List[Dict]]) -> str:
        try:
            # 构造请求内容
            messages = [{'role': 'user', 'content': prompt}] if isinstance(prompt, str) else prompt
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {self.api_key}"
            }
            data = {
                "model": self.model_name,
                "input": {
                    "messages": messages
                }
            }

            response = requests.post(self.base_url, json=data, headers=headers)

            if response.status_code == 200:
                content = response.json().get('output', {}).get('text', '').strip()
                if content:
                    return content
                else:
                    raise APIInvalidResponse("Llama API 返回的响应内容为空")
            else:
                raise APIInvalidResponse(f"Llama API 请求失败，状态码：{response.status_code}")

        except requests.RequestException as e:
            raise APIError(f"API 请求发生错误: {str(e)}")

    def set_temperature(self, temperature: float):
        if 0.0 <= temperature <= 1.0:
            self.temperature = temperature
        else:
            raise APIParameterError("温度必须在 0 到 1 之间")

    def set_max_tokens(self, max_tokens: int):
        if max_tokens > 0:
            self.max_tokens = max_tokens
        else:
            raise APIParameterError("max_tokens 必须大于 0")

    def set_top_p(self, top_p: float):
        if 0.0 <= top_p <= 1.0:
            self.top_p = top_p
        else:
            raise APIParameterError("top_p 必须在 0 到 1 之间")

    def set_model_name(self, model_name: str):
        self.model_name = model_name

    def set_base_url(self, base_url: str):
        self.base_url = base_url

    def __repr__(self):
        return f"Llama(api_key='****', model_name='{self.model_name}', base_url='{self.base_url}', temperature={self.temperature}, max_tokens={self.max_tokens}, top_p={self.top_p})"


