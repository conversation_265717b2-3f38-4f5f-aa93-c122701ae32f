import socket
import requests
import json

# ----------------------------------------------------------------
IP = 'nginx-dsjyyb-2-dev.ai-org-dev'
PORT = 136
try:
    with socket.create_connection((IP, PORT), timeout=5):
        print(f'Connection to {IP}:{PORT} succeeded!')
except(socket.timeout, socket.error) as error:
    print(f'Connection to {IP}:{PORT} failed: {error}')
# ----------------------------------------------------------------
sql = """
SELECT 
    SUM(ITEM_HOTEL_AMOUNT) AS TOTAL_HOTEL_EXPENSE
FROM 
    TW_M_FI_EXP_TRAVEL_CLAIM_HOTELS_ITEMS
WHERE 
    PROV_NAME = '集成公司'
    AND CREATE_DEPT_DESC = '智慧城市平台部'
"""
response = requests.post(
    url=f'http://{IP}:{PORT}/awesome-pluto/sql-query/executeSql',
    json={'sql': sql})
print(response.text)
# print(json.loads(response.text))
# ----------------------------------------------------------------
