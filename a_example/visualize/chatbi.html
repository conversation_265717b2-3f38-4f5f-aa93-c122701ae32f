<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8"/>
    <title>ECharts</title>
    <!-- 引入刚刚下载的 ECharts 文件 -->
    <script src="echarts.js"></script>
</head>
<body>

<div id="container" style="width: 750px; height: 600px;"></div>

<script>
    let chart = echarts.init(document.getElementById('container'));
    /* let options = {} */
    let options = {"title": {"text": "住宿费报账情况", "x": "center", "y": "top", "textStyle": {"color": "#333", "fontSize": 24, "fontWeight": "bold"}}, "tooltip": {"trigger": "axis", "axisPointer": {"type": "cross", "crossStyle": {"color": "#000"}}, "backgroundColor": "rgba(0, 0, 0, 0.7)", "textStyle": {"color": "#fff"}}, "grid": [{"top": "20%", "left": "5%", "right": "5%", "height": "60%", "containLabel": true}], "toolbox": {}, "xAxis": [{"type": "category", "boundaryGap": true, "gridIndex": 0, "data": ["alice", "bob"], "axisTick": {"alignWithLabel": true}, "axisLabel": {"textStyle": {"color": "#666"}}}], "yAxis": [{"type": "value", "gridIndex": 0, "min": 17.8, "axisLabel": {"formatter": "{value}", "textStyle": {"color": "#666"}}, "axisLine": {"lineStyle": {"color": "#ccc"}}, "axisTick": {"lineStyle": {"color": "#ccc"}}}], "dataZoom": [{"show": true, "realtime": true, "start": 0, "end": 100, "xAxisIndex": [0], "top": "85%", "height": "2%"}], "series": [{"name": "age", "type": "bar", "barWidth": "auto", "xAxisIndex": 0, "yAxisIndex": 0, "smooth": true, "itemStyle": {"opacity": 0.9, "color": {"type": "linear", "x": 0, "y": 0, "x2": 0, "y2": 1, "colorStops": [{"offset": 0, "color": "rgba(239, 102, 88, 1.0)"}, {"offset": 1, "color": "rgba(239, 102, 88, 0.1)"}], "global": false}, "shadowBlur": 5, "shadowColor": "rgba(0, 0, 0, 0.3)", "shadowOffsetX": 2, "shadowOffsetY": 2}, "label": {"show": true, "position": "top", "color": "#333", "fontSize": 12, "fontWeight": "bold"}, "data": [18.0, 20.0]}]}

    chart.setOption(options);
</script>
</body>
</html>