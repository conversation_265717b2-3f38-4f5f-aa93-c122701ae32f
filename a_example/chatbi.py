import requests
from utils import *

# ~.~.~.~.~.~.~.~.~.~.~.~.~.~.~.~.~.~.~.~
BASE_URL = ADDRESS + '/jijian/chatbiv3/add/schema'
PATHS = [
    './dataset/T1_差旅报账住宿费明细月表.xlsx',
    './dataset/T2_差旅报账费用明细月表.xlsx',
    './dataset/T3_差旅报账支付明细月表.xlsx'
]
USER = 'jijian'
SOURCE = 'jijian_ds'
# ~.~.~.~.~.~.~.~.~.~.~.~.~.~.~.~.~.~.~.~

# 请求头
headers = gen_proxy_header()
# 请求体（表单数据）
data = {
    "user": USER,
    "source": SOURCE
}
files = []
for path in PATHS:
    files.append(("files", (path.split("/")[-1], open(path, "rb"), "application/vnd.ms-excel")))

# 使用 files 参数上传文件，data 参数传递其他数据
response = requests.post(BASE_URL, headers=headers, data=data, files=files)
print(response.json())

for _, (_, f, _) in files:
    f.close()
