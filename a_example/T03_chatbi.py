from utils import *

# ~.~.~.~.~.~.~.~.~.~.~.~.~.~.~.~.~.~.~.~
BASE_URL = ADDRESS + '/jijian/chatbiv3/chat/completions'
USER = 'jijian'
SOURCE = 'jijian_ds'
MODEL = 'chatbi-o1'
LLM_NAME = 'deepseek-v3'
# QUERY = """都有哪些数据"""
QUERY = """查询河南公司差旅报账住宿费"""
SESSION_ID = 'xxxxx'
# ~.~.~.~.~.~.~.~.~.~.~.~.~.~.~.~.~.~.~.~

# 请求头
headers = gen_proxy_header()

# 请求体（JSON 数据）
data = {
    "user": USER,
    "source": SOURCE,
    "model": MODEL,
    "llm_name": LLM_NAME,
    "messages": [
        {
            "role": "user",
            "content": QUERY,
            "session_id": SESSION_ID,
        }
    ],
    "stream": True,
    "cache": False
}

print(f'用户提问: {QUERY}')
try:
    with requests.post(url=BASE_URL, headers=headers, json=data, stream=True) as response:
        if response.status_code == 200:
            for event in stream_response(response):
                if event:  # 忽略心跳消息
                    try:
                        event_data = json.loads(event)
                        content = event_data.get("choices", [{}])[0].get("delta", {}).get("content", "")
                        if content:
                            print(content, end="", flush=True)
                    except json.JSONDecodeError:
                        print(f"\n[非JSON事件] {event}")
        else:
            print(f"请求失败 (HTTP {response.status_code}): {response.text}")

except requests.exceptions.RequestException as e:
    print(f"请求异常: {str(e)}")
except KeyboardInterrupt:
    print("\n\n用户中断")
