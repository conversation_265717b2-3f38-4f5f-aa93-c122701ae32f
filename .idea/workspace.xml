<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="4a9b5649-3a6b-4e55-bb06-26f4014aa113" name="Changes" comment="commit 20250903">
      <change beforePath="$PROJECT_DIR$/.idea/workspace.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/workspace.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/a_example/T03_chatbi.py" beforeDir="false" afterPath="$PROJECT_DIR$/a_example/T03_chatbi.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/a_example/utils.py" beforeDir="false" afterPath="$PROJECT_DIR$/a_example/utils.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/engines/agents/deep_reasoning/agent_deep_reasoning.py" beforeDir="false" afterPath="$PROJECT_DIR$/engines/agents/deep_reasoning/agent_deep_reasoning.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/engines/agents/sql/agent_sql_with_dr.py" beforeDir="false" afterPath="$PROJECT_DIR$/engines/agents/sql/agent_sql_with_dr.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/engines/models/chatbi_o1.py" beforeDir="false" afterPath="$PROJECT_DIR$/engines/models/chatbi_o1.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/engines/models/chatbi_turbo.py" beforeDir="false" afterPath="$PROJECT_DIR$/engines/models/chatbi_turbo.py" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Python Script" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="HighlightingSettingsPerFile">
    <setting file="file://$PROJECT_DIR$/wrc/follow.py" root0="FORCE_HIGHLIGHTING" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;customColor&quot;: &quot;&quot;,
  &quot;associatedIndex&quot;: 2
}</component>
  <component name="ProjectId" id="31ud5xQ6BWMwZV8670kFmcIwYAP" />
  <component name="ProjectLevelVcsManager">
    <ConfirmationsSetting value="2" id="Add" />
  </component>
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "Python.T01_register.executor": "Run",
    "Python.T02_add_schema.executor": "Run",
    "Python.T03_chatbi.executor": "Run",
    "Python.api_server.executor": "Run",
    "Python.chatbi.executor": "Run",
    "Python.demo.executor": "Run",
    "Python.demo_add_schema.executor": "Run",
    "Python.requirements.executor": "Run",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "git-widget-placeholder": "main",
    "last_opened_file_path": "D:/Workspace/PyCharm/chatbi-jijian/demo_0911",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "settings.editor.selected.configurable": "com.jetbrains.python.configuration.PyActiveSdkModuleConfigurable",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="D:\Workspace\PyCharm\chatbi-jijian\demo_0911" />
      <recent name="D:\Workspace\PyCharm\chatbi-jijian\wrc" />
      <recent name="D:\Workspace\PyCharm\chatbi-jijian\engines\agents\sql" />
      <recent name="D:\Workspace\PyCharm\chatbi-jijian\engines\agents\chart" />
      <recent name="D:\Workspace\PyCharm\chatbi-jijian\engines\agents\summary" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="D:\Workspace\PyCharm\chatbi-jijian\engines\agents\sql" />
      <recent name="D:\Workspace\PyCharm\chatbi-jijian\engines\agents\deep_reasoning" />
      <recent name="D:\Workspace\PyCharm\chatbi-jijian\engines\agents\show_data" />
    </key>
  </component>
  <component name="RunManager" selected="Python.demo_add_schema">
    <configuration name="T02_add_schema" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="chatbi-jijian" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/a_example" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/a_example/T02_add_schema.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="T03_chatbi" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="chatbi-jijian" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/a_example" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/a_example/T03_chatbi.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="api_server" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="chatbi-jijian" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/api_server.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="chatbi" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="chatbi-jijian" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/a_example" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/a_example/chatbi.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="demo_add_schema" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="chatbi-jijian" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/demo_0911" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/demo_0911/demo_add_schema.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Python.demo_add_schema" />
        <item itemvalue="Python.T03_chatbi" />
        <item itemvalue="Python.T02_add_schema" />
        <item itemvalue="Python.api_server" />
        <item itemvalue="Python.chatbi" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-d6986cc7102b-410509235cf1-JavaScript-PY-242.20224.347" />
        <option value="bundled-python-sdk-c2d6afa66fbe-39cff9de6eef-com.jetbrains.pycharm.pro.sharedIndexes.bundled-PY-242.20224.347" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="4a9b5649-3a6b-4e55-bb06-26f4014aa113" name="Changes" comment="" />
      <created>1756377713789</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1756377713789</updated>
      <workItem from="1756377713935" duration="1196000" />
      <workItem from="1756379585428" duration="19000" />
      <workItem from="1756379606653" duration="4326000" />
      <workItem from="1756796381643" duration="6596000" />
      <workItem from="1756809824744" duration="10856000" />
      <workItem from="1756868502159" duration="2223000" />
      <workItem from="1756881977068" duration="5046000" />
      <workItem from="1756948922775" duration="3337000" />
      <workItem from="1757298869284" duration="11397000" />
      <workItem from="1757383607286" duration="3606000" />
      <workItem from="1757469851240" duration="2189000" />
      <workItem from="1757559719802" duration="977000" />
    </task>
    <task id="LOCAL-00001" summary="commit 20250902">
      <option name="closed" value="true" />
      <created>1756824873781</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1756824873781</updated>
    </task>
    <task id="LOCAL-00002" summary="commit 20250903">
      <option name="closed" value="true" />
      <created>1756883001498</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1756883001498</updated>
    </task>
    <option name="localTasksCounter" value="3" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="commit 20250902" />
    <MESSAGE value="commit 20250903" />
    <option name="LAST_COMMIT_MESSAGE" value="commit 20250903" />
  </component>
  <component name="com.intellij.coverage.CoverageDataManagerImpl">
    <SUITE FILE_PATH="coverage/chatbi_jijian$demo.coverage" NAME="demo Coverage Results" MODIFIED="1757312753434" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/lib" />
    <SUITE FILE_PATH="coverage/chatbi_jijian$T03_chatbi.coverage" NAME="T03_chatbi Coverage Results" MODIFIED="1757328152205" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/a_example" />
    <SUITE FILE_PATH="coverage/chatbi_jijian$T02_add_schema.coverage" NAME="T02_add_schema Coverage Results" MODIFIED="1757328061816" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/a_example" />
    <SUITE FILE_PATH="coverage/chatbi_jijian$demo_add_schema.coverage" NAME="demo_add_schema Coverage Results" MODIFIED="1757560460131" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/demo_0911" />
    <SUITE FILE_PATH="coverage/chatbi_jijian$T01_register.coverage" NAME="T01_register Coverage Results" MODIFIED="1756799831364" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/a_example" />
    <SUITE FILE_PATH="coverage/chatbi_jijian$chatbi.coverage" NAME="chatbi Coverage Results" MODIFIED="1756802361303" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/a_example" />
    <SUITE FILE_PATH="coverage/chatbi_jijian$requirements.coverage" NAME="requirements Coverage Results" MODIFIED="1756800887439" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/chatbi_jijian$api_server.coverage" NAME="api_server Coverage Results" MODIFIED="1757328049801" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
  </component>
</project>