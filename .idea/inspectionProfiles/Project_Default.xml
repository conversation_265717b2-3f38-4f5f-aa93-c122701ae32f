<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="Eslint" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="PyPackageRequirementsInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredPackages">
        <value>
          <list size="2">
            <item index="0" class="java.lang.String" itemvalue="pydantic" />
            <item index="1" class="java.lang.String" itemvalue="langchain_core" />
          </list>
        </value>
      </option>
    </inspection_tool>
    <inspection_tool class="PyUnresolvedReferencesInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredIdentifiers">
        <list>
          <option value="lancedb.query.LanceQueryBuilder.metric" />
        </list>
      </option>
    </inspection_tool>
    <inspection_tool class="ReassignedToPlainText" enabled="false" level="WARNING" enabled_by_default="false" />
    <inspection_tool class="TsLint" enabled="true" level="WARNING" enabled_by_default="true" />
  </profile>
</component>