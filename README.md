# ChatBI-纪检 🤖📊

> 基于大语言模型的智能数据分析与问答系统，专为纪检监察领域打造

[![Python](https://img.shields.io/badge/Python-3.8+-blue.svg)](https://www.python.org/)
[![FastAPI](https://img.shields.io/badge/FastAPI-0.110.0-green.svg)](https://fastapi.tiangolo.com/)
[![License](https://img.shields.io/badge/License-MIT-yellow.svg)](LICENSE)

## 📖 项目简介

ChatBI-纪检是一个专为纪检监察领域设计的智能数据分析与问答系统。通过集成大语言模型、自然语言处理和数据可视化技术，实现了从自然语言查询到SQL生成、数据分析、图表可视化的全流程自动化。

### 🎯 核心价值

- **智能问数**：支持自然语言查询，自动转换为SQL语句并执行
- **深度思维**：集成深度推理链，提供类似ChatGPT-o1的思考过程
- **数据可视化**：自动生成多种类型的交互式图表
- **知识增强**：基于RAG技术的检索增强生成，提供更准确的分析结果
- **专业定制**：针对纪检监察业务场景深度优化

### 👥 目标用户

- 纪检监察部门的数据分析人员
- 需要进行数据查询和分析的业务人员
- 数据科学家和BI开发者
- 对智能数据分析感兴趣的研究人员

## ✨ 功能特性

### 🧠 智能NL2SQL
- **自然语言理解**：支持复杂的中文自然语言查询
- **深度推理链**：集成ChatGPT-o1风格的深度思维过程
- **SQL自动生成**：智能生成高质量的SQL查询语句
- **错误自修复**：自动检测和修复SQL语法错误
- **多数据库支持**：兼容MySQL、SQL Server、SQLite等主流数据库

### 📊 数据可视化
- **智能图表推荐**：根据数据特征自动推荐最适合的图表类型
- **多样化图表**：支持柱状图、折线图、饼图、散点图、雷达图等
- **交互式展示**：基于ECharts的动态交互式图表
- **自定义配置**：支持图表样式、颜色、布局的个性化定制

### 🔍 RAG检索增强生成
- **文档智能处理**：支持PDF、Word、Excel、Markdown等多种格式
- **向量化存储**：基于Embedding的高效文档检索
- **混合检索**：结合关键词和语义检索的混合策略
- **上下文增强**：利用检索到的相关信息增强回答质量

### 🕸️ 知识图谱
- **自动构建**：从文本中智能提取实体关系构建知识图谱
- **图谱查询**：支持基于知识图谱的复杂查询
- **关系推理**：利用图谱结构进行关系推理和发现

### 🔧 SQL工具集
- **条件解析**：智能解析SQL查询条件
- **方言转换**：支持不同数据库方言之间的SQL转换
- **性能优化**：自动优化SQL查询性能
- **安全检查**：SQL注入防护和权限控制

### 🚀 高性能架构
- **异步处理**：基于FastAPI的高性能异步架构
- **流式输出**：支持实时流式响应
- **连接池管理**：智能数据库连接池管理
- **缓存优化**：多层缓存策略提升响应速度

### 🔐 企业级安全
- **权限控制**：细粒度的用户权限管理
- **数据脱敏**：敏感数据自动脱敏处理
- **审计日志**：完整的操作审计日志
- **加密传输**：端到端数据加密传输

## 🛠️ 技术栈

### 后端框架
- **[FastAPI](https://fastapi.tiangolo.com/)** - 高性能异步Web框架
- **[Uvicorn](https://www.uvicorn.org/)** - ASGI服务器
- **[Pydantic](https://pydantic-docs.helpmanual.io/)** - 数据验证和序列化

### 大语言模型集成
- **[LangChain](https://langchain.com/)** - LLM应用开发框架
- **[LangChain Community](https://github.com/langchain-ai/langchain)** - 社区扩展组件
- **[OpenAI API](https://openai.com/api/)** - GPT模型接口
- **自定义LLM适配器** - 支持多种大模型

### 数据处理与分析
- **[Pandas](https://pandas.pydata.org/)** - 数据分析和处理
- **[NumPy](https://numpy.org/)** - 数值计算
- **[SQLAlchemy](https://www.sqlalchemy.org/)** - 数据库ORM
- **[SQLGlot](https://github.com/tobymao/sqlglot)** - SQL解析和转换

### 数据库支持
- **[MySQL](https://www.mysql.com/)** - 关系型数据库
- **[SQL Server](https://www.microsoft.com/sql-server)** - 微软数据库
- **[SQLite](https://www.sqlite.org/)** - 轻量级数据库
- **[Redis](https://redis.io/)** - 内存数据库和缓存

### 搜索与向量化
- **[Elasticsearch](https://www.elastic.co/)** - 全文搜索引擎
- **[Scikit-learn](https://scikit-learn.org/)** - 机器学习库
- **Embedding模型** - 文本向量化

### 数据可视化
- **[ECharts](https://echarts.apache.org/)** - 交互式图表库
- **[PyEcharts](https://pyecharts.org/)** - Python图表库
- **[Matplotlib](https://matplotlib.org/)** - 静态图表生成

### 文档处理
- **[PDFPlumber](https://github.com/jsvine/pdfplumber)** - PDF文档解析
- **[python-docx](https://python-docx.readthedocs.io/)** - Word文档处理
- **[openpyxl](https://openpyxl.readthedocs.io/)** - Excel文件处理
- **[BeautifulSoup4](https://www.crummy.com/software/BeautifulSoup/)** - HTML解析

### 开发工具
- **[Loguru](https://loguru.readthedocs.io/)** - 日志管理
- **[PyYAML](https://pyyaml.org/)** - YAML配置文件
- **[Requests](https://requests.readthedocs.io/)** - HTTP客户端
- **[HTTPX](https://www.python-httpx.org/)** - 异步HTTP客户端

## 🚀 快速开始

### 环境要求

- **Python**: 3.8+
- **内存**: 建议8GB以上
- **存储**: 建议10GB以上可用空间
- **操作系统**: Linux/macOS/Windows

### 安装步骤

#### 1. 克隆项目

```bash
git clone https://github.com/your-org/chatbi-jijian.git
cd chatbi-jijian
```

#### 2. 创建虚拟环境

```bash
# 使用conda
conda create -n chatbi python=3.8
conda activate chatbi

# 或使用venv
python -m venv chatbi
source chatbi/bin/activate  # Linux/macOS
# chatbi\Scripts\activate  # Windows
```

#### 3. 安装依赖

```bash
# 安装Python依赖
pip install -r requirements.txt

# 或使用脚本安装
chmod +x requirements.sh
./requirements.sh
```

#### 4. 配置环境

复制并编辑配置文件：

```bash
cp servers/configs.py.example servers/configs.py
```

编辑 `servers/configs.py` 配置以下关键参数：

```python
# 大语言模型配置
llm_base_url = "your_llm_endpoint"
llm_api_key = "your_api_key"
llm_model_name = "your_model_name"

# 数据库配置
DATABASE_URL = "your_database_url"

# Redis配置（可选）
REDIS_URL = "redis://localhost:6379"

# Elasticsearch配置（可选）
ES_HOST = "localhost:9200"
```

#### 5. 启动服务

```bash
# 开发模式启动
python api_server.py

# 或使用uvicorn
uvicorn api_server:app --host 0.0.0.0 --port 8576 --reload
```

服务启动后，访问 `http://localhost:8576` 即可使用。

### Docker部署

#### 1. 构建镜像

```bash
docker build -t chatbi-jijian .
```

#### 2. 运行容器

```bash
docker run -d \
  --name chatbi \
  -p 8576:8576 \
  -v $(pwd)/static:/app/static \
  -e LLM_API_KEY=your_api_key \
  chatbi-jijian
```

#### 3. 使用Docker Compose

```bash
docker-compose up -d
```

## 📚 使用指南

### API接口

#### 1. 用户注册

```bash
curl -X POST "http://localhost:8576/jijian/chatbiv3/register" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "your_username",
    "email": "<EMAIL>"
  }'
```

#### 2. 上传数据源

```bash
curl -X POST "http://localhost:8576/jijian/chatbiv3/add/schema" \
  -H "Content-Type: multipart/form-data" \
  -F "file=@your_data.xlsx" \
  -F "user=your_username" \
  -F "source_name=your_datasource"
```

#### 3. 智能问答

```bash
curl -X POST "http://localhost:8576/jijian/chatbiv3/chat/completions" \
  -H "Content-Type: application/json" \
  -d '{
    "user": "your_username",
    "source": "your_datasource",
    "model": "chatbi-o1",
    "messages": [
      {
        "role": "user",
        "content": "查询河南公司差旅报账住宿费",
        "session_id": "session_123"
      }
    ],
    "stream": true
  }'
```

### Python SDK使用示例

#### 1. 基础NL2SQL

```python
from chatbi.agent_modules.nl2sql import BasicSQLAgent
from chatbi.data_source.dbms import MySQLDataSource
from chatbi.llms import ChatBILLM

# 初始化组件
llm = ChatBILLM(
    base_url="your_llm_endpoint",
    api_key="your_api_key",
    model_name="your_model"
)

datasource = MySQLDataSource(
    host="localhost",
    port=3306,
    database="your_db",
    username="your_user",
    password="your_password"
)

# 创建SQL生成器
sql_agent = BasicSQLAgent(llm=llm)

# 生成SQL
query = "查询销售额最高的前10个产品"
sql = sql_agent.generate_sql(query=query, datasource=datasource)
print(f"生成的SQL: {sql}")

# 执行查询
result = datasource.execute(sql)
print(f"查询结果: {result}")
```

#### 2. 数据可视化

```python
from chatbi.agent_modules.charts import ChartAgent
import pandas as pd

# 准备数据
data = pd.DataFrame({
    'product': ['A', 'B', 'C', 'D'],
    'sales': [100, 200, 150, 300]
})

# 创建图表生成器
chart_agent = ChartAgent(llm=llm)

# 生成图表
chart_config = chart_agent.generate_chart(
    data=data,
    query="生成一个销售额柱状图"
)

# 导出HTML
html_content = chart_agent.to_echarts(
    filename="sales_chart.html",
    width="800px",
    height="600px"
)
```

#### 3. RAG检索增强

```python
from chatbi.rag import RAG
from chatbi.rag.embedding import EmbeddingModel

# 初始化RAG系统
embedding = EmbeddingModel(api_key="your_embedding_api_key")
rag = RAG(
    llm=llm,
    embedding=embedding,
    chunk_size=1000,
    chunk_overlap=100
)

# 加载文档
rag.load_document("path/to/your/document.pdf")

# 智能问答
query = "这份文档的主要内容是什么？"
answer = rag.chat(query=query)
print(f"回答: {answer}")
```

#### 4. 知识图谱构建

```python
from chatbi.kgraph import KnowledgeGraph

# 创建知识图谱
kg = KnowledgeGraph()

# 从文本构建
text = "张三是ABC公司的销售经理，负责华东地区的业务。"
kg.add_triplets_from_text(text=text, llm=llm)

# 查询关系
relations = kg.get_relations("张三")
print(f"张三的关系: {relations}")

# 导出图谱
kg_json = kg.export_for_llm(format='json')
```

### 命令行工具

#### 1. 数据源管理

```bash
# 查看所有数据源
python -m chatbi.data_source list

# 测试数据源连接
python -m chatbi.data_source test --source your_datasource

# 导出数据源schema
python -m chatbi.data_source export --source your_datasource --output schema.json
```

#### 2. 批量SQL生成

```bash
# 从文件批量生成SQL
python -m chatbi.agent_modules.nl2sql batch \
  --input queries.txt \
  --output results.json \
  --datasource your_datasource
```

## 📁 项目结构

```
chatbi-jijian/
├── 📁 chatbi/                    # 核心业务模块
│   ├── 📁 agent/                 # Agent基础框架
│   ├── 📁 agent_modules/         # 功能模块
│   │   ├── 📁 charts/            # 数据可视化
│   │   ├── 📁 edit_df/           # 数据框编辑
│   │   └── 📁 nl2sql/            # 自然语言转SQL
│   ├── 📁 data_source/           # 数据源管理
│   │   ├── 📁 dbms/              # 数据库适配器
│   │   ├── 📁 connection/        # 连接池管理
│   │   └── 📁 adaptive_schema/   # 自适应Schema
│   ├── 📁 llms/                  # 大语言模型集成
│   ├── 📁 rag/                   # 检索增强生成
│   │   ├── 📁 spliter/           # 文档分割
│   │   ├── 📁 embedding/         # 向量化
│   │   ├── 📁 storage/           # 向量存储
│   │   └── 📁 retriever/         # 检索器
│   ├── 📁 kgraph/                # 知识图谱
│   ├── 📁 sqlforge/              # SQL工具集
│   ├── 📁 memory/                # 记忆管理
│   ├── 📁 similarity/            # 相似度计算
│   └── 📁 utils/                 # 工具函数
├── 📁 engines/                   # 执行引擎
│   ├── 📁 agents/                # 智能代理
│   ├── 📁 models/                # 模型封装
│   └── 📁 helper/                # 辅助工具
├── 📁 servers/                   # 服务层
│   ├── 📁 authorization/         # 认证授权
│   ├── 📁 datasources/           # 数据源服务
│   └── 📁 models/                # 模型服务
├── 📁 a_example/                 # 使用示例
│   ├── 📁 dataset/               # 示例数据
│   ├── 📁 result/                # 结果展示
│   └── 📁 visualize/             # 可视化示例
├── 📁 static/                    # 静态资源
│   ├── 📁 logs/                  # 日志文件
│   ├── 📁 pickle/                # 序列化数据
│   └── 📁 resource/              # 资源文件
├── 📄 api_server.py              # API服务入口
├── 📄 requirements.txt           # Python依赖
└── 📄 README.md                  # 项目文档
```

### 核心模块说明

#### 🧠 chatbi/agent_modules/
- **nl2sql/**: 自然语言转SQL核心模块，支持复杂查询理解和SQL生成
- **charts/**: 智能图表生成，支持多种图表类型和自定义配置
- **edit_df/**: 数据框编辑功能，支持数据清洗和转换

#### 🗄️ chatbi/data_source/
- **dbms/**: 多数据库适配器，支持MySQL、SQL Server、SQLite等
- **connection/**: 数据库连接池管理，提供高效的连接复用
- **adaptive_schema/**: 自适应Schema解析，智能理解数据结构

#### 🤖 chatbi/llms/
- **chatbi_llm.py**: 统一的LLM接口封装
- **balancer.py**: 负载均衡器，支持多模型切换
- **method/**: 各种LLM调用方法的实现

#### 🔍 chatbi/rag/
- **spliter/**: 支持PDF、Word、Excel等多种文档格式的智能分割
- **embedding/**: 文本向量化，支持多种Embedding模型
- **storage/**: 向量存储，支持内存和Elasticsearch
- **retriever/**: 检索器，支持语义检索和混合检索

#### 🕸️ chatbi/kgraph/
- **knowledge_graph.py**: 知识图谱构建和查询
- **prompts/**: 知识抽取的提示模板

#### 🔧 chatbi/sqlforge/
- **condition_parser.py**: SQL条件解析器
- **dialect_translate.py**: SQL方言转换
- **sql_transform.py**: SQL优化和转换

## 🤝 贡献指南

我们欢迎所有形式的贡献！无论是Bug报告、功能建议、代码贡献还是文档改进。

### 如何贡献

#### 1. 报告Bug

如果您发现了Bug，请在[GitHub Issues](https://github.com/your-org/chatbi-jijian/issues)中创建一个新的issue，并包含以下信息：

- **Bug描述**：清晰描述遇到的问题
- **复现步骤**：详细的复现步骤
- **期望行为**：描述您期望的正确行为
- **环境信息**：操作系统、Python版本、依赖版本等
- **错误日志**：相关的错误信息和堆栈跟踪

#### 2. 功能建议

对于新功能建议，请：

- 在Issues中详细描述功能需求
- 说明功能的使用场景和价值
- 如果可能，提供设计思路或实现方案

#### 3. 代码贡献

1. **Fork项目**到您的GitHub账户
2. **创建分支**：`git checkout -b feature/your-feature-name`
3. **编写代码**：遵循项目的代码规范
4. **添加测试**：为新功能添加相应的测试用例
5. **提交更改**：`git commit -m "Add: your feature description"`
6. **推送分支**：`git push origin feature/your-feature-name`
7. **创建Pull Request**：详细描述您的更改

### 开发规范

#### 代码风格

- 遵循[PEP 8](https://www.python.org/dev/peps/pep-0008/)Python代码规范
- 使用有意义的变量名和函数名
- 添加适当的注释和文档字符串
- 保持代码简洁和可读性

#### 提交信息规范

使用以下格式的提交信息：

```
<type>(<scope>): <description>

[optional body]

[optional footer]
```

类型（type）包括：
- `feat`: 新功能
- `fix`: Bug修复
- `docs`: 文档更新
- `style`: 代码格式调整
- `refactor`: 代码重构
- `test`: 测试相关
- `chore`: 构建过程或辅助工具的变动

#### 测试要求

- 为新功能编写单元测试
- 确保所有测试通过
- 测试覆盖率应保持在80%以上

### 开发环境设置

```bash
# 1. 克隆您的fork
git clone https://github.com/your-username/chatbi-jijian.git
cd chatbi-jijian

# 2. 添加上游仓库
git remote add upstream https://github.com/original-org/chatbi-jijian.git

# 3. 创建开发环境
python -m venv venv
source venv/bin/activate  # Linux/macOS
# venv\Scripts\activate  # Windows

# 4. 安装开发依赖
pip install -r requirements.txt
pip install -r requirements-dev.txt  # 如果存在

# 5. 运行测试
python -m pytest tests/

# 6. 代码格式检查
flake8 chatbi/
black chatbi/
```

## 📄 许可证

本项目采用 [MIT License](LICENSE) 开源许可证。

```
MIT License

Copyright (c) 2025 ChatBI-纪检项目团队

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.
```

## 📞 联系我们

- **项目主页**: [GitHub Repository](https://github.com/your-org/chatbi-jijian)
- **问题反馈**: [GitHub Issues](https://github.com/your-org/chatbi-jijian/issues)
- **讨论交流**: [GitHub Discussions](https://github.com/your-org/chatbi-jijian/discussions)
- **邮箱联系**: <EMAIL>

## 🙏 致谢

感谢以下开源项目和贡献者：

- [LangChain](https://langchain.com/) - 为LLM应用开发提供了强大的框架
- [FastAPI](https://fastapi.tiangolo.com/) - 高性能的Web框架
- [ECharts](https://echarts.apache.org/) - 优秀的数据可视化库
- [SQLGlot](https://github.com/tobymao/sqlglot) - 强大的SQL解析和转换工具
- 所有为项目贡献代码、文档和建议的开发者们

## 📈 项目状态

- ✅ **核心功能**：NL2SQL、数据可视化、RAG检索
- ✅ **基础架构**：微服务架构、API接口
- 🚧 **进行中**：性能优化、功能扩展
- 📋 **计划中**：移动端支持、更多数据源

---

<div align="center">

**如果这个项目对您有帮助，请给我们一个⭐️！**

[⭐ Star](https://github.com/your-org/chatbi-jijian) | [🍴 Fork](https://github.com/your-org/chatbi-jijian/fork) | [📝 Issues](https://github.com/your-org/chatbi-jijian/issues)

</div>
